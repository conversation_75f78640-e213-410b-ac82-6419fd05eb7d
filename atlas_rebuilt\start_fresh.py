#!/usr/bin/env python3
"""
Start A.T.L.A.S fresh with real API keys
"""

import os
import sys
import subprocess
import signal
import psutil

def kill_existing_servers():
    """Kill any existing Python servers on port 8080"""
    print("🔄 Checking for existing servers...")
    
    killed_any = False
    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
        try:
            if proc.info['name'] and 'python' in proc.info['name'].lower():
                cmdline = ' '.join(proc.info['cmdline'] or [])
                if 'atlas_server' in cmdline or 'uvicorn' in cmdline or '8080' in cmdline:
                    print(f"🔪 Killing existing server: PID {proc.info['pid']}")
                    proc.kill()
                    killed_any = True
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            pass
    
    if killed_any:
        print("✅ Existing servers terminated")
    else:
        print("✅ No existing servers found")

def get_alpaca_secret():
    """Get the real Alpaca secret key"""
    print("\n🔑 ALPACA SECRET KEY SETUP")
    print("=" * 40)
    print("You need your real Alpaca secret key for live trading.")
    print("This is different from the API key (which we have).")
    print()
    print("🔍 Where to find it:")
    print("  1. Go to https://app.alpaca.markets")
    print("  2. Login to your account")
    print("  3. Go to 'API Keys' section")
    print("  4. Copy your SECRET KEY (not the API key)")
    print()
    
    secret_key = input("🔐 Enter your Alpaca SECRET KEY: ").strip()
    
    if not secret_key:
        print("❌ No secret key provided!")
        return None
    
    if len(secret_key) < 20:
        print("⚠️ That seems too short for an Alpaca secret key...")
        confirm = input("Continue anyway? (y/n): ").lower()
        if confirm != 'y':
            return None
    
    return secret_key

def start_atlas_production(secret_key):
    """Start A.T.L.A.S with the real secret key"""
    print("\n🚀 STARTING A.T.L.A.S PRODUCTION MODE")
    print("=" * 50)
    
    # Set environment variables
    env = os.environ.copy()
    env.update({
        "VALIDATION_MODE": "false",
        "ALPACA_API_KEY": "PKI0KNC8HXZURYRA4OMC",
        "ALPACA_SECRET_KEY": secret_key,
        "FMP_API_KEY": "K63wnAbUDHNPICjzeq3JcEG1vi8Q2oz7",
        "OPENAI_API_KEY": "********************************************************************************************************************************************************************",
        "PREDICTO_API_KEY": "VZ19mf7DvVovUKW0E7PTYmzrJkQjkC5N5fMcWwOsglWPFzhSQPU8m77cb3d3k760"
    })
    
    print("✅ API Keys configured")
    print("🔥 PRODUCTION MODE: ACTIVE")
    print("📊 Real market data: ENABLED")
    print("🧠 Full AI analysis: ENABLED")
    print("💼 Live trading: ENABLED")
    print("📈 Predicto predictions: ENABLED")
    
    # Start the server
    try:
        print("\n🌐 Starting server...")
        subprocess.run([sys.executable, "atlas_server_fixed.py"], env=env, check=True)
    except KeyboardInterrupt:
        print("\n⏹️ Server stopped by user")
    except Exception as e:
        print(f"❌ Server error: {e}")

def main():
    """Main function"""
    print("🔥 A.T.L.A.S FRESH START - PRODUCTION MODE")
    print("=" * 50)
    
    # Kill existing servers
    kill_existing_servers()
    
    # Get Alpaca secret key
    secret_key = get_alpaca_secret()
    if not secret_key:
        print("❌ Cannot start without Alpaca secret key")
        return False
    
    # Start production server
    start_atlas_production(secret_key)
    return True

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n⏹️ Startup cancelled by user")
    except Exception as e:
        print(f"💥 Error: {e}")
        import traceback
        traceback.print_exc()
