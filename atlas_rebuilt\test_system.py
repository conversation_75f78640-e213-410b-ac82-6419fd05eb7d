#!/usr/bin/env python3
"""
A.T.L.A.S System Test - Comprehensive Validation
"""

import asyncio
import aiohttp
import json
import time
from datetime import datetime

async def test_health_endpoint():
    """Test health endpoint"""
    print("🔍 Testing health endpoint...")
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get('http://localhost:8080/api/v1/health') as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"✅ Health check passed - Status: {data.get('status')}")
                    
                    if 'engines' in data:
                        print("📊 Engine Status:")
                        for engine, status in data['engines'].items():
                            print(f"   - {engine}: {status}")
                    
                    return True
                else:
                    print(f"❌ Health check failed - Status: {response.status}")
                    return False
                    
    except Exception as e:
        print(f"❌ Health check error: {e}")
        return False

async def test_chat_endpoint():
    """Test chat endpoint"""
    print("\n💬 Testing chat endpoint...")
    
    try:
        async with aiohttp.ClientSession() as session:
            chat_data = {
                "message": "Hello A.T.L.A.S, are you working?",
                "session_id": "test-session"
            }
            
            async with session.post(
                'http://localhost:8080/api/v1/chat',
                json=chat_data
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"✅ Chat endpoint working")
                    print(f"📝 Response: {data.get('response', '')[:100]}...")
                    print(f"🎯 Type: {data.get('type')}")
                    print(f"📊 Confidence: {data.get('confidence')}")
                    return True
                else:
                    print(f"❌ Chat endpoint failed - Status: {response.status}")
                    return False
                    
    except Exception as e:
        print(f"❌ Chat endpoint error: {e}")
        return False

async def test_quote_endpoint():
    """Test quote endpoint"""
    print("\n📈 Testing quote endpoint...")
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get('http://localhost:8080/api/v1/quote/AAPL') as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"✅ Quote endpoint working")
                    print(f"📊 AAPL Price: ${data.get('price', 'N/A')}")
                    return True
                elif response.status == 503:
                    print("⚠️ Quote endpoint - system still initializing")
                    return True
                else:
                    print(f"❌ Quote endpoint failed - Status: {response.status}")
                    return False
                    
    except Exception as e:
        print(f"❌ Quote endpoint error: {e}")
        return False

async def test_scan_endpoint():
    """Test market scan endpoint"""
    print("\n🔍 Testing market scan endpoint...")
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get('http://localhost:8080/api/v1/scan') as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"✅ Scan endpoint working")
                    signals = data.get('signals', [])
                    print(f"📊 Found {len(signals)} signals")
                    return True
                elif response.status == 503:
                    print("⚠️ Scan endpoint - system still initializing")
                    return True
                else:
                    print(f"❌ Scan endpoint failed - Status: {response.status}")
                    return False
                    
    except Exception as e:
        print(f"❌ Scan endpoint error: {e}")
        return False

async def test_initialization_status():
    """Test initialization status endpoint"""
    print("\n⚙️ Testing initialization status...")
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get('http://localhost:8080/api/v1/initialization/status') as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"✅ Initialization status endpoint working")
                    
                    components = data.get('components', {})
                    print("📊 Component Status:")
                    for name, info in components.items():
                        status = info.get('status', 'unknown')
                        progress = info.get('progress', 0) * 100
                        print(f"   - {name}: {status} ({progress:.1f}%)")
                    
                    return True
                else:
                    print(f"❌ Initialization status failed - Status: {response.status}")
                    return False
                    
    except Exception as e:
        print(f"❌ Initialization status error: {e}")
        return False

async def wait_for_server(max_wait=30):
    """Wait for server to be available"""
    print(f"⏳ Waiting for server to start (max {max_wait}s)...")
    
    for i in range(max_wait):
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get('http://localhost:8080/api/v1/health', timeout=aiohttp.ClientTimeout(total=2)) as response:
                    if response.status in [200, 503]:  # 503 is OK during initialization
                        print(f"✅ Server is responding after {i+1}s")
                        return True
        except:
            pass
        
        await asyncio.sleep(1)
        if i % 5 == 4:
            print(f"   Still waiting... ({i+1}s)")
    
    print(f"❌ Server not responding after {max_wait}s")
    return False

async def run_comprehensive_test():
    """Run comprehensive system test"""
    print("🚀 A.T.L.A.S System Comprehensive Test")
    print("=" * 50)
    print(f"📅 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Wait for server
    if not await wait_for_server():
        print("❌ Server not available - test aborted")
        return False
    
    # Run tests
    tests = [
        ("Health Endpoint", test_health_endpoint),
        ("Chat Endpoint", test_chat_endpoint),
        ("Quote Endpoint", test_quote_endpoint),
        ("Scan Endpoint", test_scan_endpoint),
        ("Initialization Status", test_initialization_status)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🧪 Running {test_name}...")
        try:
            if await test_func():
                passed += 1
            else:
                print(f"❌ {test_name} failed")
        except Exception as e:
            print(f"❌ {test_name} error: {e}")
    
    # Summary
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("✅ All tests passed - A.T.L.A.S system is working correctly!")
        return True
    else:
        print(f"⚠️ {total - passed} tests failed - system may have issues")
        return False

def main():
    """Main test function"""
    try:
        result = asyncio.run(run_comprehensive_test())
        return result
    except KeyboardInterrupt:
        print("\n🛑 Test interrupted by user")
        return False
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        return False

if __name__ == "__main__":
    import sys
    success = main()
    sys.exit(0 if success else 1)
