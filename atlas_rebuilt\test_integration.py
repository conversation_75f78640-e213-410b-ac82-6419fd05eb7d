#!/usr/bin/env python3
"""
A.T.L.A.S Integration Testing Suite
Tests component communication and orchestrator coordination
"""

import asyncio
import logging
import sys
from datetime import datetime
from typing import Dict, Any

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_component_initialization():
    """Test that all components initialize properly"""
    print("🔧 Testing Component Initialization...")
    
    try:
        from atlas_orchestrator import AtlasOrchestrator
        
        orchestrator = AtlasOrchestrator()
        
        # Test component initialization
        components_to_test = [
            ("ai_engine", "AI Engine"),
            ("market_engine", "Market Engine"), 
            ("trading_engine", "Trading Engine"),
            ("risk_engine", "Risk Engine"),
            ("education_engine", "Education Engine")
        ]
        
        results = {}
        
        for component_name, display_name in components_to_test:
            try:
                print(f"  Testing {display_name}...")
                
                if component_name == "ai_engine":
                    component = await orchestrator._ensure_ai_engine()
                elif component_name == "market_engine":
                    component = await orchestrator._ensure_market_engine()
                elif component_name == "trading_engine":
                    component = await orchestrator._ensure_trading_engine()
                elif component_name == "risk_engine":
                    component = await orchestrator._ensure_risk_engine()
                elif component_name == "education_engine":
                    component = await orchestrator._ensure_education_engine()
                
                if component:
                    results[component_name] = "✅ PASS"
                    print(f"    ✅ {display_name} initialized successfully")
                else:
                    results[component_name] = "⚠️ DEGRADED"
                    print(f"    ⚠️ {display_name} in degraded mode")
                    
            except Exception as e:
                results[component_name] = f"❌ FAIL: {str(e)}"
                print(f"    ❌ {display_name} failed: {e}")
        
        return results
        
    except Exception as e:
        print(f"❌ Component initialization test failed: {e}")
        return {"error": str(e)}

async def test_orchestrator_communication():
    """Test orchestrator message processing and component coordination"""
    print("\n🔄 Testing Orchestrator Communication...")
    
    try:
        from atlas_orchestrator import AtlasOrchestrator
        
        orchestrator = AtlasOrchestrator()
        
        # Test message processing
        test_messages = [
            "What is RSI?",
            "Analyze AAPL for trading",
            "I want to make $100 today",
            "Show me TTM Squeeze signals"
        ]
        
        results = {}
        
        for i, message in enumerate(test_messages, 1):
            try:
                print(f"  Test {i}: Processing '{message[:30]}...'")
                
                response = await orchestrator.process_message(message, f"test-session-{i}")
                
                if response and hasattr(response, 'response'):
                    results[f"message_{i}"] = "✅ PASS"
                    print(f"    ✅ Response received ({len(response.response)} chars)")
                else:
                    results[f"message_{i}"] = "❌ FAIL: No response"
                    print(f"    ❌ No response received")
                    
            except Exception as e:
                results[f"message_{i}"] = f"❌ FAIL: {str(e)}"
                print(f"    ❌ Message processing failed: {e}")
        
        return results
        
    except Exception as e:
        print(f"❌ Orchestrator communication test failed: {e}")
        return {"error": str(e)}

async def test_component_interactions():
    """Test interactions between different components"""
    print("\n🔗 Testing Component Interactions...")
    
    try:
        from atlas_orchestrator import AtlasOrchestrator
        
        orchestrator = AtlasOrchestrator()
        
        # Ensure components are initialized
        ai_engine = await orchestrator._ensure_ai_engine()
        market_engine = await orchestrator._ensure_market_engine()
        trading_engine = await orchestrator._ensure_trading_engine()
        risk_engine = await orchestrator._ensure_risk_engine()
        education_engine = await orchestrator._ensure_education_engine()
        
        results = {}
        
        # Test 1: AI Engine + Market Engine interaction
        try:
            print("  Testing AI Engine + Market Engine...")
            if market_engine:
                quote = await market_engine.get_quote("AAPL")
                if quote:
                    results["ai_market_interaction"] = "✅ PASS"
                    print("    ✅ Market data retrieved successfully")
                else:
                    results["ai_market_interaction"] = "⚠️ DEGRADED"
                    print("    ⚠️ Market data in fallback mode")
            else:
                results["ai_market_interaction"] = "❌ SKIP"
                print("    ❌ Market engine not available")
        except Exception as e:
            results["ai_market_interaction"] = f"❌ FAIL: {str(e)}"
            print(f"    ❌ AI-Market interaction failed: {e}")
        
        # Test 2: Trading Engine + Risk Engine interaction
        try:
            print("  Testing Trading Engine + Risk Engine...")
            if trading_engine and risk_engine:
                portfolio_summary = trading_engine.get_portfolio_summary()
                risk_analysis = await trading_engine.analyze_portfolio_risk()
                
                if portfolio_summary and risk_analysis:
                    results["trading_risk_interaction"] = "✅ PASS"
                    print("    ✅ Portfolio and risk analysis working")
                else:
                    results["trading_risk_interaction"] = "⚠️ DEGRADED"
                    print("    ⚠️ Portfolio analysis in fallback mode")
            else:
                results["trading_risk_interaction"] = "❌ SKIP"
                print("    ❌ Trading or Risk engine not available")
        except Exception as e:
            results["trading_risk_interaction"] = f"❌ FAIL: {str(e)}"
            print(f"    ❌ Trading-Risk interaction failed: {e}")
        
        # Test 3: Education Engine interaction
        try:
            print("  Testing Education Engine...")
            if education_engine:
                from models import EducationRequest
                
                request = EducationRequest(
                    question="What is technical analysis?",
                    difficulty_level="beginner"
                )
                
                response = await education_engine.process_query(request)
                
                if response and hasattr(response, 'response'):
                    results["education_interaction"] = "✅ PASS"
                    print("    ✅ Educational query processed")
                else:
                    results["education_interaction"] = "⚠️ DEGRADED"
                    print("    ⚠️ Education in fallback mode")
            else:
                results["education_interaction"] = "❌ SKIP"
                print("    ❌ Education engine not available")
        except Exception as e:
            results["education_interaction"] = f"❌ FAIL: {str(e)}"
            print(f"    ❌ Education interaction failed: {e}")
        
        return results
        
    except Exception as e:
        print(f"❌ Component interaction test failed: {e}")
        return {"error": str(e)}

async def run_integration_tests():
    """Run comprehensive integration test suite"""
    print("🚀 A.T.L.A.S Integration Testing Suite")
    print("=" * 50)
    print(f"Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    all_results = {}
    
    # Test 1: Component Initialization
    init_results = await test_component_initialization()
    all_results["component_initialization"] = init_results
    
    # Test 2: Orchestrator Communication
    comm_results = await test_orchestrator_communication()
    all_results["orchestrator_communication"] = comm_results
    
    # Test 3: Component Interactions
    interaction_results = await test_component_interactions()
    all_results["component_interactions"] = interaction_results
    
    # Calculate overall results
    total_tests = 0
    passed_tests = 0
    
    for category, results in all_results.items():
        if isinstance(results, dict) and "error" not in results:
            for test_name, result in results.items():
                total_tests += 1
                if result.startswith("✅"):
                    passed_tests += 1
    
    success_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0
    
    # Summary
    print("\n" + "=" * 50)
    print("🎯 INTEGRATION TEST SUMMARY")
    print("=" * 50)
    
    print(f"Total Tests: {total_tests}")
    print(f"Passed: {passed_tests}")
    print(f"Success Rate: {success_rate:.1f}%")
    
    if success_rate >= 80:
        print("🎉 EXCELLENT - Integration working well!")
        status = "EXCELLENT"
    elif success_rate >= 60:
        print("✅ GOOD - Most integrations working")
        status = "GOOD"
    elif success_rate >= 40:
        print("⚠️ FAIR - Some integration issues")
        status = "FAIR"
    else:
        print("❌ POOR - Major integration problems")
        status = "POOR"
    
    print(f"\nCompleted at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    return success_rate >= 60

if __name__ == "__main__":
    try:
        success = asyncio.run(run_integration_tests())
        sys.exit(0 if success else 1)
    except Exception as e:
        print(f"💥 Integration test failed with error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
