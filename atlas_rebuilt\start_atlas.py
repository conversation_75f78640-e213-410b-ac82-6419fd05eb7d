#!/usr/bin/env python3
"""
A.T.L.A.S AI Trading System - Startup Script
Production-ready startup with comprehensive validation
"""

import sys
import os
import asyncio
import logging
from datetime import datetime

def setup_logging():
    """Setup basic logging for startup"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('atlas_startup.log')
        ]
    )
    return logging.getLogger(__name__)

def validate_environment():
    """Validate environment setup"""
    logger = logging.getLogger(__name__)
    
    logger.info("🔍 Validating environment setup...")
    
    # Check Python version
    if sys.version_info < (3, 8):
        logger.error("❌ Python 3.8+ required")
        return False
    
    logger.info(f"✅ Python {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}")
    
    # Check required files
    required_files = [
        'atlas_server.py',
        'atlas_orchestrator.py', 
        'config.py',
        'models.py',
        '.env'
    ]
    
    for file in required_files:
        if not os.path.exists(file):
            logger.error(f"❌ Missing required file: {file}")
            return False
        logger.info(f"✅ Found {file}")
    
    # Check environment variables
    from dotenv import load_dotenv
    load_dotenv()
    
    required_env_vars = [
        'APCA_API_KEY_ID',
        'APCA_API_SECRET_KEY', 
        'FMP_API_KEY',
        'OPENAI_API_KEY'
    ]
    
    for var in required_env_vars:
        if not os.getenv(var):
            logger.error(f"❌ Missing environment variable: {var}")
            return False
        logger.info(f"✅ Environment variable {var} configured")
    
    return True

def install_dependencies():
    """Install required dependencies"""
    logger = logging.getLogger(__name__)
    
    logger.info("📦 Checking dependencies...")
    
    required_packages = [
        'fastapi',
        'uvicorn',
        'pydantic',
        'pydantic-settings',
        'python-dotenv',
        'aiohttp',
        'openai'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            logger.info(f"✅ {package} available")
        except ImportError:
            missing_packages.append(package)
            logger.warning(f"⚠️ {package} not found")
    
    if missing_packages:
        logger.info(f"📦 Installing missing packages: {', '.join(missing_packages)}")
        import subprocess
        try:
            subprocess.check_call([
                sys.executable, '-m', 'pip', 'install'
            ] + missing_packages)
            logger.info("✅ Dependencies installed successfully")
        except subprocess.CalledProcessError as e:
            logger.error(f"❌ Failed to install dependencies: {e}")
            return False
    
    return True

def test_configuration():
    """Test configuration loading"""
    logger = logging.getLogger(__name__)
    
    logger.info("⚙️ Testing configuration...")
    
    try:
        from config import settings, validate_environment
        logger.info("✅ Configuration imported successfully")
        
        env_status = validate_environment()
        if env_status["valid"]:
            logger.info("✅ Environment validation passed")
        else:
            logger.warning(f"⚠️ Environment issues: {env_status['errors']}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Configuration test failed: {e}")
        return False

def start_server():
    """Start the A.T.L.A.S server"""
    logger = logging.getLogger(__name__)
    
    logger.info("🚀 Starting A.T.L.A.S AI Trading System...")
    logger.info("=" * 60)
    logger.info("🎯 Advanced Trading & Learning Analysis System")
    logger.info("⚡ Non-blocking architecture - server responds immediately")
    logger.info("🌐 Web interface: http://localhost:8080")
    logger.info("📚 API docs: http://localhost:8080/docs")
    logger.info("🔍 Health check: http://localhost:8080/api/v1/health")
    logger.info("=" * 60)
    
    try:
        import uvicorn
        from atlas_server import app
        
        # Start server
        uvicorn.run(
            app,
            host="0.0.0.0",
            port=8080,
            reload=False,
            log_level="info"
        )
        
    except KeyboardInterrupt:
        logger.info("🛑 Server stopped by user")
    except Exception as e:
        logger.error(f"❌ Server startup failed: {e}")
        return False
    
    return True

def main():
    """Main startup function"""
    logger = setup_logging()
    
    logger.info("🚀 A.T.L.A.S AI Trading System - Startup Sequence")
    logger.info(f"📅 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    logger.info(f"📁 Working directory: {os.getcwd()}")
    
    # Validation steps
    steps = [
        ("Environment Validation", validate_environment),
        ("Dependency Check", install_dependencies),
        ("Configuration Test", test_configuration)
    ]
    
    for step_name, step_func in steps:
        logger.info(f"🔄 {step_name}...")
        if not step_func():
            logger.error(f"❌ {step_name} failed - aborting startup")
            return False
        logger.info(f"✅ {step_name} completed")
    
    # Start server
    logger.info("🎯 All validation steps passed - starting server...")
    return start_server()

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
