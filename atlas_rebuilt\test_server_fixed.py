#!/usr/bin/env python3
"""
Test the fixed A.T.L.A.S server startup and functionality
"""

import asyncio
import aiohttp
import os
import sys
import time
import subprocess
from datetime import datetime

# Set validation mode
os.environ['VALIDATION_MODE'] = 'true'

async def test_server_endpoints():
    """Test server endpoints once it's running"""
    print("🔍 Testing A.T.L.A.S Server Endpoints...")
    
    # Wait for server to start
    print("⏳ Waiting for server to start...")
    await asyncio.sleep(3)
    
    endpoints_to_test = [
        ("GET", "/api/v1/health", "Health Check"),
        ("GET", "/api/v1/initialization/status", "Initialization Status"),
        ("POST", "/api/v1/chat", "Chat Interface", {
            "message": "What is RSI?",
            "session_id": "test-session"
        }),
        ("GET", "/api/v1/quote/AAPL", "Market Quote"),
        ("GET", "/api/v1/scan?min_strength=moderate", "Market Scanner"),
        ("GET", "/api/v1/portfolio", "Portfolio Summary"),
    ]
    
    results = {}
    
    async with aiohttp.ClientSession() as session:
        for method, endpoint, name, *data in endpoints_to_test:
            try:
                print(f"  Testing {name} ({method} {endpoint})...")
                
                if method == "GET":
                    async with session.get(f"http://localhost:8080{endpoint}") as response:
                        status = response.status
                        content = await response.text()
                elif method == "POST":
                    payload = data[0] if data else {}
                    async with session.post(
                        f"http://localhost:8080{endpoint}", 
                        json=payload
                    ) as response:
                        status = response.status
                        content = await response.text()
                
                if status == 200:
                    results[name] = "✅ PASS"
                    print(f"    ✅ {name} working (Status: {status})")
                elif status == 503:
                    results[name] = "⚠️ INITIALIZING"
                    print(f"    ⚠️ {name} initializing (Status: {status})")
                else:
                    results[name] = f"❌ FAIL ({status})"
                    print(f"    ❌ {name} failed (Status: {status})")
                    
            except Exception as e:
                results[name] = f"❌ ERROR: {str(e)[:50]}..."
                print(f"    ❌ {name} error: {e}")
    
    return results

def test_server_import():
    """Test if the server can be imported without errors"""
    print("📦 Testing Server Import...")
    
    try:
        # Test basic imports
        from atlas_server_fixed import app
        print("✅ Server imports successfully")
        
        # Test FastAPI app
        if app:
            print("✅ FastAPI app created")
            print(f"✅ App title: {app.title}")
            print(f"✅ App version: {app.version}")
        else:
            print("❌ FastAPI app is None")
            
        return True
        
    except Exception as e:
        print(f"❌ Import error: {e}")
        import traceback
        traceback.print_exc()
        return False

async def run_comprehensive_test():
    """Run comprehensive server test"""
    print("🚀 A.T.L.A.S Server Comprehensive Test")
    print("=" * 50)
    print(f"Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Test 1: Import Test
    import_success = test_server_import()
    
    if not import_success:
        print("\n❌ Server import failed - cannot proceed with endpoint tests")
        return False
    
    print("\n🌐 Starting server for endpoint testing...")
    
    # Start server in background
    try:
        server_process = subprocess.Popen([
            sys.executable, "atlas_server_fixed.py"
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        print("⏳ Server starting...")
        
        # Test endpoints
        endpoint_results = await test_server_endpoints()
        
        # Calculate results
        total_tests = len(endpoint_results)
        passed_tests = sum(1 for result in endpoint_results.values() 
                          if result.startswith("✅"))
        initializing_tests = sum(1 for result in endpoint_results.values() 
                               if result.startswith("⚠️"))
        
        success_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0
        
        # Summary
        print("\n" + "=" * 50)
        print("🎯 SERVER TEST SUMMARY")
        print("=" * 50)
        
        print(f"Import Test: {'✅ PASS' if import_success else '❌ FAIL'}")
        print(f"Total Endpoint Tests: {total_tests}")
        print(f"Passed: {passed_tests}")
        print(f"Initializing: {initializing_tests}")
        print(f"Success Rate: {success_rate:.1f}%")
        
        if success_rate >= 80:
            print("🎉 EXCELLENT - Server working well!")
            status = "EXCELLENT"
        elif success_rate >= 60:
            print("✅ GOOD - Most endpoints working")
            status = "GOOD"
        elif success_rate >= 40:
            print("⚠️ FAIR - Some endpoints working")
            status = "FAIR"
        else:
            print("❌ POOR - Major server issues")
            status = "POOR"
        
        print(f"\nDetailed Results:")
        for test_name, result in endpoint_results.items():
            print(f"  {result} {test_name}")
        
        # Cleanup
        server_process.terminate()
        server_process.wait(timeout=5)
        
        return success_rate >= 60
        
    except Exception as e:
        print(f"❌ Server test failed: {e}")
        return False

if __name__ == "__main__":
    try:
        success = asyncio.run(run_comprehensive_test())
        print(f"\n🏁 Test completed: {'SUCCESS' if success else 'FAILED'}")
        sys.exit(0 if success else 1)
        
    except KeyboardInterrupt:
        print("\n⏹️ Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"💥 Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
