"""
A.T.L.A.S AI Trading System - Minimal Working Server
Simplified server for immediate testing and validation
"""

import asyncio
import logging
import os
from datetime import datetime
from typing import Optional, Dict, Any

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import HTMLResponse

# Set validation mode
os.environ.setdefault('VALIDATION_MODE', 'true')

# Configure basic logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Global state
orchestrator = None
system_status = "initializing"

# Initialize FastAPI app
app = FastAPI(
    title="A.T.L.A.S AI Trading System",
    description="Advanced Trading & Learning Analysis System - Minimal Server",
    version="4.0.0-minimal"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.on_event("startup")
async def startup_event():
    """Initialize system on startup"""
    global orchestrator, system_status
    
    logger.info("Starting A.T.L.A.S AI Trading System - Minimal Server")
    logger.info("Validation mode enabled - limited functionality for testing")
    
    try:
        # Import and initialize orchestrator
        from atlas_orchestrator import AtlasOrchestrator
        
        orchestrator = AtlasOrchestrator()
        system_status = "ready"
        
        logger.info("A.T.L.A.S system ready for testing")
        
    except Exception as e:
        logger.error(f"System initialization failed: {e}")
        system_status = "failed"

@app.get("/")
async def root():
    """Root endpoint - serve simple interface"""
    return HTMLResponse("""
    <!DOCTYPE html>
    <html>
    <head>
        <title>A.T.L.A.S AI Trading System</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 40px; background: #1a1a1a; color: #fff; }
            .container { max-width: 800px; margin: 0 auto; }
            .status { padding: 20px; border-radius: 8px; margin: 20px 0; }
            .ready { background: #2d5a2d; }
            .initializing { background: #5a5a2d; }
            .failed { background: #5a2d2d; }
            .chat-box { background: #2a2a2a; padding: 20px; border-radius: 8px; margin: 20px 0; }
            input, textarea { width: 100%; padding: 10px; margin: 10px 0; background: #333; color: #fff; border: 1px solid #555; border-radius: 4px; }
            button { padding: 10px 20px; background: #007acc; color: white; border: none; border-radius: 4px; cursor: pointer; }
            button:hover { background: #005a99; }
            .response { background: #1e3a1e; padding: 15px; border-radius: 4px; margin: 10px 0; }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🚀 A.T.L.A.S AI Trading System</h1>
            <p>Advanced Trading & Learning Analysis System</p>
            
            <div class="status" id="status-box">
                <h3>System Status: <span id="status">Loading...</span></h3>
                <p>Validation Mode: Active (Testing without API keys)</p>
            </div>
            
            <div class="chat-box">
                <h3>💬 Chat with A.T.L.A.S</h3>
                <textarea id="message" placeholder="Ask me about trading, technical analysis, or market insights..." rows="3"></textarea>
                <button onclick="sendMessage()">Send Message</button>
                <div id="response" class="response" style="display: none;"></div>
            </div>
            
            <div class="chat-box">
                <h3>🔍 Quick Tests</h3>
                <button onclick="testHealth()">Test Health</button>
                <button onclick="testQuote()">Test Market Quote</button>
                <button onclick="testEducation()">Test Education</button>
                <div id="test-results"></div>
            </div>
        </div>
        
        <script>
            // Update status
            async function updateStatus() {
                try {
                    const response = await fetch('/api/v1/health');
                    const data = await response.json();
                    document.getElementById('status').textContent = data.status;
                    
                    const statusBox = document.getElementById('status-box');
                    statusBox.className = 'status ' + data.status;
                } catch (e) {
                    document.getElementById('status').textContent = 'Error';
                }
            }
            
            // Send chat message
            async function sendMessage() {
                const message = document.getElementById('message').value;
                if (!message) return;
                
                try {
                    const response = await fetch('/api/v1/chat', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({
                            message: message,
                            session_id: 'web-session'
                        })
                    });
                    
                    const data = await response.json();
                    const responseDiv = document.getElementById('response');
                    responseDiv.innerHTML = '<strong>A.T.L.A.S:</strong> ' + data.response;
                    responseDiv.style.display = 'block';
                    
                } catch (e) {
                    document.getElementById('response').innerHTML = 'Error: ' + e.message;
                    document.getElementById('response').style.display = 'block';
                }
            }
            
            // Test functions
            async function testHealth() {
                try {
                    const response = await fetch('/api/v1/health');
                    const data = await response.json();
                    showTestResult('Health Check', data);
                } catch (e) {
                    showTestResult('Health Check', { error: e.message });
                }
            }
            
            async function testQuote() {
                try {
                    const response = await fetch('/api/v1/quote/AAPL');
                    const data = await response.json();
                    showTestResult('Market Quote', data);
                } catch (e) {
                    showTestResult('Market Quote', { error: e.message });
                }
            }
            
            async function testEducation() {
                try {
                    const response = await fetch('/api/v1/education', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({
                            question: 'What is RSI?',
                            difficulty_level: 'beginner'
                        })
                    });
                    const data = await response.json();
                    showTestResult('Education Query', data);
                } catch (e) {
                    showTestResult('Education Query', { error: e.message });
                }
            }
            
            function showTestResult(testName, data) {
                const resultsDiv = document.getElementById('test-results');
                resultsDiv.innerHTML = '<h4>' + testName + ' Result:</h4><pre>' + JSON.stringify(data, null, 2) + '</pre>';
            }
            
            // Update status every 5 seconds
            setInterval(updateStatus, 5000);
            updateStatus();
        </script>
    </body>
    </html>
    """)

@app.get("/api/v1/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": system_status,
        "timestamp": datetime.now().isoformat(),
        "mode": "validation",
        "message": "A.T.L.A.S minimal server running"
    }

@app.post("/api/v1/chat")
async def chat_endpoint(request: Dict[str, Any]):
    """Chat endpoint"""
    try:
        if not orchestrator:
            return {
                "response": "A.T.L.A.S is still initializing. Please wait a moment...",
                "type": "system_status",
                "confidence": 0.5
            }
        
        message = request.get("message", "")
        session_id = request.get("session_id", "default")
        
        logger.info(f"Processing chat message: {message[:50]}...")
        
        # Process through orchestrator
        response = await orchestrator.process_message(message, session_id)
        
        return {
            "response": response.response if hasattr(response, 'response') else str(response),
            "type": getattr(response, 'type', 'chat'),
            "confidence": getattr(response, 'confidence', 0.8)
        }
        
    except Exception as e:
        logger.error(f"Chat error: {e}")
        return {
            "response": f"I encountered an error: {str(e)}. Please try again.",
            "type": "error",
            "confidence": 0.0
        }

@app.get("/api/v1/quote/{symbol}")
async def get_quote(symbol: str):
    """Get market quote"""
    try:
        if not orchestrator:
            raise HTTPException(status_code=503, detail="System initializing")
        
        # Simulate quote data in validation mode
        return {
            "symbol": symbol.upper(),
            "price": 150.25,
            "change": 2.15,
            "change_percent": 1.45,
            "volume": 45678900,
            "timestamp": datetime.now().isoformat(),
            "mode": "validation"
        }
        
    except Exception as e:
        logger.error(f"Quote error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/v1/education")
async def education_query(request: Dict[str, Any]):
    """Education endpoint"""
    try:
        if not orchestrator:
            raise HTTPException(status_code=503, detail="System initializing")
        
        question = request.get("question", "")
        
        # Simple educational response
        if "rsi" in question.lower():
            response = "RSI (Relative Strength Index) is a momentum oscillator that measures the speed and change of price movements. It ranges from 0 to 100, with readings above 70 typically considered overbought and below 30 oversold."
        else:
            response = f"Thank you for your question about '{question}'. In validation mode, I can provide basic educational responses. For full functionality, please configure API keys."
        
        return {
            "response": response,
            "type": "education",
            "confidence": 0.8,
            "mode": "validation"
        }
        
    except Exception as e:
        logger.error(f"Education error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

if __name__ == "__main__":
    import uvicorn
    
    print("🚀 Starting A.T.L.A.S Minimal Server...")
    print("📍 Server will be available at: http://localhost:8080")
    print("🔍 Health check: http://localhost:8080/api/v1/health")
    print("💬 Web interface: http://localhost:8080")
    
    uvicorn.run(
        "atlas_server_minimal:app",
        host="0.0.0.0",
        port=8080,
        reload=False,
        log_level="info"
    )
