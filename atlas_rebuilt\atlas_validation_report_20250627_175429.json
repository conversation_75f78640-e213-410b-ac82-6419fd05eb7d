{"system_name": "A.T.L.A.S AI Trading System", "validation_timestamp": "2025-06-27T17:54:29.282977", "total_tests": 7, "passed_tests": 3, "failed_tests": 3, "warning_tests": 0, "skipped_tests": 0, "error_tests": 1, "overall_status": "failed", "summary": {"total_execution_time": 8.086456, "success_rate": 42.857142857142854, "critical_issues": 4, "warnings": 0, "components_tested": 6, "categories_tested": 7}, "recommendations": ["Address 3 critical failures before deployment", "Initialize missing components: server"], "results_by_category": {"feature_coverage": [{"test_name": "feature_coverage_validation", "category": "feature_coverage", "status": "passed", "message": "All documented features are implemented", "details": {"documented_features": ["Configuration System", "AI Engine", "Trading Engine", "Market Engine", "Education Engine", "Risk Engine", "Orchestrator"], "implemented_features": ["Configuration System", "AI Engine", "Trading Engine", "Market Engine", "Education Engine", "Risk Engine", "Orchestrator"], "missing_implementations": [], "undocumented_features": [], "coverage_percentage": 100.0}, "execution_time": 0.000413, "timestamp": "2025-06-27T17:54:28.982013"}], "api_endpoints": [{"test_name": "api_endpoints_validation", "category": "api_endpoints", "status": "error", "message": "Validation error: cannot access local variable 'documented_endpoints' where it is not associated with a value", "details": {"error": "cannot access local variable 'documented_endpoints' where it is not associated with a value"}, "execution_time": 0.001506, "timestamp": "2025-06-27T17:54:28.983956"}], "integration": [{"test_name": "integration_validation", "category": "integration", "status": "failed", "message": "Integration failures: 1", "details": {"successful_integrations": ["orchestrator.ai_engine", "orchestrator.trading_engine", "orchestrator.market_engine", "orchestrator.education_engine", "orchestrator.risk_engine", "ai_engine_has_initialize", "ai_engine_has_process_message", "trading_engine_has_initialize", "trading_engine_has_place_order", "market_engine_has_initialize", "market_engine_has_get_quote", "education_engine_has_initialize", "risk_engine_has_initialize"], "failed_integrations": ["server_not_available"], "components_available": 6, "total_components": 7}, "execution_time": 0.00024, "timestamp": "2025-06-27T17:54:28.984442"}], "critical_path": [{"test_name": "critical_paths_validation", "category": "critical_path", "status": "passed", "message": "All critical paths functional", "details": {"successful_paths": ["ai_engine.process_message_available", "market_engine_component_available", "trading_engine.place_order_available", "risk_engine_component_available", "education_engine_component_available", "trade_confirmation_available", "portfolio_management_available", "education_query_available"], "failed_paths": [], "total_paths_tested": 8}, "execution_time": 0.000197, "timestamp": "2025-06-27T17:54:28.984830"}], "safety_risk": [{"test_name": "safety_risk_validation", "category": "safety_risk", "status": "failed", "message": "Safety system failures: 2", "details": {"safety_systems_operational": ["Trading Engine Safety", "Risk Management Engine", "AI Safety Guardrails", "Core Safety Architecture", "goal_reality_checking"], "safety_system_failures": ["trade_risk_assessment_missing", "portfolio_risk_monitoring_missing"], "safety_coverage": 71.42857142857143}, "execution_time": 0.000187, "timestamp": "2025-06-27T17:54:28.985205"}], "documentation": [{"test_name": "documentation_validation", "category": "documentation", "status": "passed", "message": "Documentation complete and accurate", "details": {"documentation_complete": ["readme_has_installation", "readme_has_quick start", "readme_has_features", "readme_has_api", "readme_has_configuration", "api_documentation_present", "installation_instructions_present", "requirements_file_present", "config_file_present"], "documentation_issues": [], "completeness_score": 100.0}, "execution_time": 0.002614, "timestamp": "2025-06-27T17:54:28.988024"}], "dependencies": [{"test_name": "dependencies_validation", "category": "dependencies", "status": "failed", "message": "Critical dependencies missing: 1", "details": {"dependencies_satisfied": ["configuration_system_valid", "validation_mode_active", "core_package_fastapi_available", "core_package_uvicorn_available", "core_package_aiohttp_available", "core_package_pydantic_available", "config_config.py_present", "config_models.py_present", "api_config_openai_present", "api_config_fmp_present", "api_config_predicto_present"], "dependency_issues": ["api_config_alpaca_missing"], "fallback_mechanisms": ["orchestrator_has_error_handling", "ai_engine_has_error_handling", "trading_engine_has_error_handling", "market_engine_has_error_handling", "education_engine_has_error_handling", "risk_engine_has_error_handling"], "critical_missing": ["api_config_alpaca_missing"], "dependency_coverage": 91.66666666666666}, "execution_time": 0.294666, "timestamp": "2025-06-27T17:54:29.282894"}]}}