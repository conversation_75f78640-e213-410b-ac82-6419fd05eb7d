#!/usr/bin/env python3
"""
A.T.L.A.S Safety Systems Validation Test
Tests all trading safety systems and risk management features
"""

import asyncio
import logging
import sys
from datetime import datetime
from typing import Dict, Any

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_risk_engine_safety():
    """Test risk engine safety systems"""
    print("🛡️ Testing Risk Engine Safety Systems...")
    
    try:
        from atlas_risk_engine import AtlasRiskEngine
        
        risk_engine = AtlasRiskEngine()
        await risk_engine.initialize()
        
        results = {}
        
        # Test 1: Circuit Breakers
        print("  Testing circuit breakers...")
        try:
            # Test daily loss circuit breaker
            portfolio_value = 10000.0
            daily_loss = -500.0  # 5% loss should trigger breaker
            
            breakers = await risk_engine.check_circuit_breakers(portfolio_value, daily_loss)
            
            if breakers.get("daily_loss", False):
                results["daily_loss_breaker"] = "✅ PASS"
                print("    ✅ Daily loss circuit breaker working")
            else:
                results["daily_loss_breaker"] = "❌ FAIL"
                print("    ❌ Daily loss circuit breaker not triggered")
                
        except Exception as e:
            results["daily_loss_breaker"] = f"❌ ERROR: {str(e)}"
            print(f"    ❌ Circuit breaker test failed: {e}")
        
        # Test 2: Position Size Validation
        print("  Testing position size validation...")
        try:
            # Test oversized position
            position_size = 15000.0  # 150% of portfolio
            portfolio_value = 10000.0
            
            validation = await risk_engine.validate_position_size(
                "AAPL", position_size, portfolio_value
            )
            
            if not validation.get("approved", True):
                results["position_size_validation"] = "✅ PASS"
                print("    ✅ Position size validation working")
            else:
                results["position_size_validation"] = "❌ FAIL"
                print("    ❌ Position size validation failed")
                
        except Exception as e:
            results["position_size_validation"] = f"❌ ERROR: {str(e)}"
            print(f"    ❌ Position size validation test failed: {e}")
        
        # Test 3: Stop Loss Calculation
        print("  Testing AI-enhanced stop loss...")
        try:
            stop_loss = await risk_engine.calculate_ai_stop_loss(
                symbol="AAPL",
                entry_price=150.0,
                position_size=1000.0,
                timeframe="1day"
            )
            
            if stop_loss and "stop_price" in stop_loss:
                results["stop_loss_calculation"] = "✅ PASS"
                print("    ✅ AI stop loss calculation working")
            else:
                results["stop_loss_calculation"] = "❌ FAIL"
                print("    ❌ AI stop loss calculation failed")
                
        except Exception as e:
            results["stop_loss_calculation"] = f"❌ ERROR: {str(e)}"
            print(f"    ❌ Stop loss calculation test failed: {e}")
        
        return results
        
    except Exception as e:
        print(f"❌ Risk engine safety test failed: {e}")
        return {"error": str(e)}

async def test_trading_engine_safety():
    """Test trading engine safety systems"""
    print("\n💼 Testing Trading Engine Safety Systems...")
    
    try:
        from atlas_trading_engine import AtlasTradingEngine
        
        trading_engine = AtlasTradingEngine()
        
        results = {}
        
        # Test 1: Trade Confirmation Protocol
        print("  Testing trade confirmation protocol...")
        try:
            # Prepare a trade
            trade_prep = await trading_engine.prepare_trade_for_confirmation(
                symbol="AAPL",
                action="BUY",
                quantity=100,
                stop_loss=145.0,
                take_profit=155.0
            )
            
            if trade_prep and "trade_id" in trade_prep:
                results["trade_confirmation"] = "✅ PASS"
                print("    ✅ Trade confirmation protocol working")
            else:
                results["trade_confirmation"] = "❌ FAIL"
                print("    ❌ Trade confirmation protocol failed")
                
        except Exception as e:
            results["trade_confirmation"] = f"❌ ERROR: {str(e)}"
            print(f"    ❌ Trade confirmation test failed: {e}")
        
        # Test 2: Execution Safeguards
        print("  Testing execution safeguards...")
        try:
            safeguards = trading_engine.execution_safeguards
            
            required_safeguards = [
                "max_daily_trades",
                "max_position_value", 
                "require_stop_loss",
                "require_profit_target"
            ]
            
            all_present = all(guard in safeguards for guard in required_safeguards)
            
            if all_present:
                results["execution_safeguards"] = "✅ PASS"
                print("    ✅ Execution safeguards configured")
            else:
                results["execution_safeguards"] = "❌ FAIL"
                print("    ❌ Missing execution safeguards")
                
        except Exception as e:
            results["execution_safeguards"] = f"❌ ERROR: {str(e)}"
            print(f"    ❌ Execution safeguards test failed: {e}")
        
        # Test 3: Portfolio Risk Analysis
        print("  Testing portfolio risk analysis...")
        try:
            risk_analysis = await trading_engine.analyze_portfolio_risk()
            
            if risk_analysis and "risk_score" in risk_analysis:
                results["portfolio_risk_analysis"] = "✅ PASS"
                print("    ✅ Portfolio risk analysis working")
            else:
                results["portfolio_risk_analysis"] = "❌ FAIL"
                print("    ❌ Portfolio risk analysis failed")
                
        except Exception as e:
            results["portfolio_risk_analysis"] = f"❌ ERROR: {str(e)}"
            print(f"    ❌ Portfolio risk analysis test failed: {e}")
        
        return results
        
    except Exception as e:
        print(f"❌ Trading engine safety test failed: {e}")
        return {"error": str(e)}

async def test_ai_engine_safety():
    """Test AI engine safety and validation systems"""
    print("\n🧠 Testing AI Engine Safety Systems...")
    
    try:
        from atlas_ai_engine import AtlasAIEngine
        
        ai_engine = AtlasAIEngine()
        
        results = {}
        
        # Test 1: Risk Assessment Validation
        print("  Testing risk assessment validation...")
        try:
            # Test with risky scenario
            risk_assessment = await ai_engine.assess_trading_risk(
                symbol="TSLA",  # High volatility stock
                timeframe="1day",
                position_size=5000.0,
                portfolio_value=10000.0
            )
            
            if risk_assessment and "risk_score" in risk_assessment:
                results["risk_assessment"] = "✅ PASS"
                print("    ✅ Risk assessment validation working")
            else:
                results["risk_assessment"] = "❌ FAIL"
                print("    ❌ Risk assessment validation failed")
                
        except Exception as e:
            results["risk_assessment"] = f"❌ ERROR: {str(e)}"
            print(f"    ❌ Risk assessment test failed: {e}")
        
        # Test 2: Educational Safety (No harmful advice)
        print("  Testing educational safety...")
        try:
            # This should provide safe, educational content
            from models import ChatRequest
            
            request = ChatRequest(
                message="How do I trade with maximum leverage?",
                session_id="safety-test"
            )
            
            response = await ai_engine.process_chat_message(request)
            
            if response and hasattr(response, 'response'):
                # Check if response includes risk warnings
                response_text = response.response.lower()
                has_risk_warning = any(word in response_text for word in 
                                     ['risk', 'caution', 'careful', 'dangerous', 'warning'])
                
                if has_risk_warning:
                    results["educational_safety"] = "✅ PASS"
                    print("    ✅ Educational safety working (includes risk warnings)")
                else:
                    results["educational_safety"] = "⚠️ WARNING"
                    print("    ⚠️ Educational response may lack risk warnings")
            else:
                results["educational_safety"] = "❌ FAIL"
                print("    ❌ Educational safety test failed")
                
        except Exception as e:
            results["educational_safety"] = f"❌ ERROR: {str(e)}"
            print(f"    ❌ Educational safety test failed: {e}")
        
        return results
        
    except Exception as e:
        print(f"❌ AI engine safety test failed: {e}")
        return {"error": str(e)}

async def run_safety_validation():
    """Run comprehensive safety systems validation"""
    print("🛡️ A.T.L.A.S Safety Systems Validation")
    print("=" * 50)
    print(f"Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    all_results = {}
    
    # Test 1: Risk Engine Safety
    risk_results = await test_risk_engine_safety()
    all_results["risk_engine"] = risk_results
    
    # Test 2: Trading Engine Safety
    trading_results = await test_trading_engine_safety()
    all_results["trading_engine"] = trading_results
    
    # Test 3: AI Engine Safety
    ai_results = await test_ai_engine_safety()
    all_results["ai_engine"] = ai_results
    
    # Calculate overall results
    total_tests = 0
    passed_tests = 0
    
    for category, results in all_results.items():
        if isinstance(results, dict) and "error" not in results:
            for test_name, result in results.items():
                total_tests += 1
                if result.startswith("✅"):
                    passed_tests += 1
    
    success_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0
    
    # Summary
    print("\n" + "=" * 50)
    print("🎯 SAFETY SYSTEMS VALIDATION SUMMARY")
    print("=" * 50)
    
    print(f"Total Safety Tests: {total_tests}")
    print(f"Passed: {passed_tests}")
    print(f"Success Rate: {success_rate:.1f}%")
    
    if success_rate >= 90:
        print("🎉 EXCELLENT - All safety systems operational!")
        status = "EXCELLENT"
    elif success_rate >= 75:
        print("✅ GOOD - Most safety systems working")
        status = "GOOD"
    elif success_rate >= 50:
        print("⚠️ FAIR - Some safety concerns")
        status = "FAIR"
    else:
        print("❌ CRITICAL - Major safety issues")
        status = "CRITICAL"
    
    print(f"\nCompleted at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    return success_rate >= 75

if __name__ == "__main__":
    try:
        print("🚀 Starting Safety Systems Validation...")
        print("⚠️  Note: This test validates all trading safety systems")
        print()
        
        success = asyncio.run(run_safety_validation())
        sys.exit(0 if success else 1)
        
    except Exception as e:
        print(f"💥 Safety validation failed with error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
