#!/usr/bin/env python3
"""
A.T.L.A.S Validation Runner
Simple script to run comprehensive system validation
"""

import asyncio
import sys
import os
import logging
from datetime import datetime

# Add current directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from atlas_validation_system import run_atlas_validation, print_validation_report, ValidationStatus

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)


async def main():
    """Main validation runner"""
    print("🚀 A.T.L.A.S AI Trading System - Comprehensive Validation")
    print("=" * 60)
    print(f"Validation started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    try:
        # Get atlas root path
        atlas_path = sys.argv[1] if len(sys.argv) > 1 else "."
        
        if not os.path.exists(atlas_path):
            print(f"❌ Error: Atlas path '{atlas_path}' does not exist")
            sys.exit(1)
        
        print(f"📁 Atlas root path: {os.path.abspath(atlas_path)}")
        print()
        
        # Run comprehensive validation
        print("🔍 Running comprehensive validation suite...")
        print("This may take a few moments...")
        print()
        
        report = await run_atlas_validation(atlas_path)
        
        # Print detailed report
        print_validation_report(report)
        
        # Generate summary
        print("\n🎯 VALIDATION SUMMARY:")
        
        if report.overall_status == ValidationStatus.PASSED:
            print("✅ VALIDATION PASSED - System is ready for deployment")
            exit_code = 0
        elif report.overall_status == ValidationStatus.WARNING:
            print("⚠️  VALIDATION PASSED WITH WARNINGS - Review recommendations")
            exit_code = 2
        else:
            print("❌ VALIDATION FAILED - Critical issues must be addressed")
            exit_code = 1
        
        # Show next steps
        print(f"\n📋 NEXT STEPS:")
        if report.failed_tests > 0:
            print("1. Address critical failures listed above")
            print("2. Re-run validation after fixes")
        elif report.warning_tests > 0:
            print("1. Review warnings and recommendations")
            print("2. Consider implementing suggested improvements")
        else:
            print("1. System validation complete!")
            print("2. Ready for production deployment")
        
        print(f"\n📄 Detailed report saved to: atlas_validation_report_*.json")
        print(f"🕐 Validation completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        sys.exit(exit_code)
        
    except KeyboardInterrupt:
        print("\n⏹️  Validation interrupted by user")
        sys.exit(130)
        
    except Exception as e:
        logger.error(f"Validation failed with error: {e}")
        print(f"\n💥 VALIDATION ERROR: {str(e)}")
        print("Please check the logs for more details")
        sys.exit(1)


def print_usage():
    """Print usage information"""
    print("Usage: python run_validation.py [atlas_root_path]")
    print()
    print("Arguments:")
    print("  atlas_root_path    Path to A.T.L.A.S root directory (default: current directory)")
    print()
    print("Examples:")
    print("  python run_validation.py")
    print("  python run_validation.py /path/to/atlas")
    print("  python run_validation.py .")
    print()
    print("Exit codes:")
    print("  0 - Validation passed")
    print("  1 - Validation failed (critical issues)")
    print("  2 - Validation passed with warnings")
    print("  130 - Interrupted by user")


if __name__ == "__main__":
    # Check for help flag
    if len(sys.argv) > 1 and sys.argv[1] in ["-h", "--help", "help"]:
        print_usage()
        sys.exit(0)
    
    # Run validation
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n⏹️  Validation interrupted")
        sys.exit(130)
