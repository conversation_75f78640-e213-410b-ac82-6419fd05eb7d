"""
A.T.L.A.S AI Trading System - Data Models and Schemas
Pydantic models for type safety and validation
"""

from datetime import datetime
from typing import Optional, List, Dict, Any, Union
from enum import Enum
from pydantic import BaseModel, Field, validator


# Enums for type safety
class OrderSide(str, Enum):
    BUY = "buy"
    SELL = "sell"


class OrderType(str, Enum):
    MARKET = "market"
    LIMIT = "limit"
    STOP = "stop"
    STOP_LIMIT = "stop_limit"


class OrderStatus(str, Enum):
    NEW = "new"
    PARTIALLY_FILLED = "partially_filled"
    FILLED = "filled"
    CANCELED = "canceled"
    REJECTED = "rejected"


class EngineStatus(str, Enum):
    INITIALIZING = "initializing"
    ACTIVE = "active"
    DEGRADED = "degraded"
    FAILED = "failed"


class SignalStrength(str, Enum):
    VERY_WEAK = "very_weak"
    WEAK = "weak"
    MODERATE = "moderate"
    STRONG = "strong"
    VERY_STRONG = "very_strong"


# Core Data Models
class Quote(BaseModel):
    """Real-time market quote"""
    symbol: str
    price: float
    bid: Optional[float] = None
    ask: Optional[float] = None
    volume: Optional[int] = None
    timestamp: datetime
    
    @validator('price')
    def validate_price(cls, v):
        if v <= 0:
            raise ValueError('Price must be positive')
        return v


class Position(BaseModel):
    """Trading position"""
    symbol: str
    quantity: float
    avg_price: float
    current_price: float
    unrealized_pnl: float
    realized_pnl: float = 0.0
    side: OrderSide
    timestamp: datetime


class Order(BaseModel):
    """Trading order"""
    id: Optional[str] = None
    symbol: str
    quantity: float
    side: OrderSide
    type: OrderType
    price: Optional[float] = None
    stop_price: Optional[float] = None
    status: OrderStatus = OrderStatus.NEW
    filled_quantity: float = 0.0
    timestamp: datetime
    
    @validator('quantity')
    def validate_quantity(cls, v):
        if v <= 0:
            raise ValueError('Quantity must be positive')
        return v


class TechnicalIndicators(BaseModel):
    """Technical analysis indicators"""
    symbol: str
    rsi: Optional[float] = None
    macd: Optional[float] = None
    macd_signal: Optional[float] = None
    macd_histogram: Optional[float] = None
    sma_20: Optional[float] = None
    sma_50: Optional[float] = None
    ema_12: Optional[float] = None
    ema_26: Optional[float] = None
    bollinger_upper: Optional[float] = None
    bollinger_lower: Optional[float] = None
    atr: Optional[float] = None
    volume_sma: Optional[float] = None
    timestamp: datetime


class TTMSqueezeSignal(BaseModel):
    """TTM Squeeze trading signal"""
    symbol: str
    signal_strength: SignalStrength
    histogram_value: float
    squeeze_active: bool
    momentum_direction: str  # "bullish", "bearish", "neutral"
    confidence: float = Field(ge=0.0, le=1.0)
    entry_price: Optional[float] = None
    stop_loss: Optional[float] = None
    target_price: Optional[float] = None
    timestamp: datetime


class RiskAssessment(BaseModel):
    """Risk management assessment"""
    symbol: str
    risk_score: float = Field(ge=0.0, le=10.0)
    position_size: float
    stop_loss_price: float
    risk_amount: float
    risk_percentage: float
    confidence_level: float = Field(ge=0.0, le=1.0)
    risk_factors: List[str] = []
    recommendations: List[str] = []
    timestamp: datetime


class MarketContext(BaseModel):
    """Market context and sentiment"""
    overall_sentiment: str  # "bullish", "bearish", "neutral"
    vix_level: Optional[float] = None
    market_trend: Optional[str] = None
    sector_rotation: Optional[Dict[str, float]] = None
    news_sentiment: Optional[float] = None
    economic_indicators: Optional[Dict[str, Any]] = None
    timestamp: datetime


# API Request/Response Models
class ChatRequest(BaseModel):
    """Chat message request"""
    message: str = Field(min_length=1, max_length=2000)
    session_id: Optional[str] = None
    context: Optional[Dict[str, Any]] = None


class AIResponse(BaseModel):
    """AI response model"""
    response: str
    type: str = "chat"  # chat, analysis, error, system_status
    confidence: float = Field(ge=0.0, le=1.0, default=0.8)
    context: Optional[Dict[str, Any]] = None
    suggestions: Optional[List[str]] = None
    timestamp: datetime = Field(default_factory=datetime.now)


class AnalysisRequest(BaseModel):
    """Market analysis request"""
    symbol: str = Field(regex=r'^[A-Z]{1,5}$')
    timeframe: str = Field(default="1day", regex=r'^(1min|5min|15min|30min|1hour|1day)$')
    include_technical: bool = True
    include_sentiment: bool = True
    include_risk: bool = True


class EducationRequest(BaseModel):
    """Educational query request"""
    question: str = Field(min_length=1, max_length=1000)
    topic: Optional[str] = None
    difficulty_level: str = Field(default="beginner", regex=r'^(beginner|intermediate|advanced)$')
    book_filter: Optional[str] = None


class SystemStatus(BaseModel):
    """System health and status"""
    status: str  # healthy, degraded, initializing, failed
    timestamp: datetime
    version: str = "4.0.0"
    engines: Dict[str, EngineStatus]
    initialization_progress: Optional[Dict[str, float]] = None
    errors: Optional[List[str]] = None
    warnings: Optional[List[str]] = None


class PortfolioSummary(BaseModel):
    """Portfolio summary"""
    total_value: float
    cash_balance: float
    positions_value: float
    unrealized_pnl: float
    realized_pnl: float
    day_change: float
    day_change_percent: float
    positions: List[Position]
    timestamp: datetime


class ScanResult(BaseModel):
    """Market scan result"""
    symbol: str
    signal_type: str
    signal_strength: SignalStrength
    score: float = Field(ge=0.0, le=10.0)
    price: float
    volume: Optional[int] = None
    indicators: Optional[Dict[str, float]] = None
    reasoning: Optional[str] = None
    timestamp: datetime


class PredictoForecast(BaseModel):
    """Predicto AI forecast"""
    symbol: str
    forecast_days: int
    predicted_price: float
    confidence: float = Field(ge=0.0, le=1.0)
    price_targets: Optional[Dict[str, float]] = None
    risk_factors: Optional[List[str]] = None
    sentiment_score: Optional[float] = None
    timestamp: datetime


# Configuration Models
class UserProfile(BaseModel):
    """User trading profile"""
    experience_level: str = Field(default="beginner", regex=r'^(beginner|intermediate|advanced|expert)$')
    risk_tolerance: str = Field(default="moderate", regex=r'^(conservative|moderate|aggressive)$')
    account_size: float = Field(default=10000.0, ge=1000.0)
    communication_style: str = Field(default="mentor", regex=r'^(mentor|professional|casual)$')
    trading_goals: Optional[List[str]] = None
    preferred_timeframes: Optional[List[str]] = None


class InitializationStatus(BaseModel):
    """System initialization tracking"""
    component: str
    status: EngineStatus
    progress: float = Field(ge=0.0, le=1.0)
    message: Optional[str] = None
    error: Optional[str] = None
    started_at: datetime
    completed_at: Optional[datetime] = None


# Export all models
__all__ = [
    # Enums
    "OrderSide", "OrderType", "OrderStatus", "EngineStatus", "SignalStrength",
    
    # Core Models
    "Quote", "Position", "Order", "TechnicalIndicators", "TTMSqueezeSignal",
    "RiskAssessment", "MarketContext",
    
    # API Models
    "ChatRequest", "AIResponse", "AnalysisRequest", "EducationRequest",
    "SystemStatus", "PortfolioSummary", "ScanResult", "PredictoForecast",
    
    # Configuration Models
    "UserProfile", "InitializationStatus"
]
