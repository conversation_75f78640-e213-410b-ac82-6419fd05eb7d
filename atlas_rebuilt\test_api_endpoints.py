#!/usr/bin/env python3
"""
API Endpoints Verification Test
Tests all documented API endpoints to ensure they work as expected
"""

import asyncio
import aiohttp
import json
import sys
from datetime import datetime

# Test configuration
BASE_URL = "http://localhost:8080"
TEST_SYMBOL = "AAPL"

async def test_endpoint(session, method, endpoint, data=None, expected_status=200):
    """Test a single API endpoint"""
    url = f"{BASE_URL}{endpoint}"
    
    try:
        if method.upper() == "GET":
            async with session.get(url) as response:
                status = response.status
                content = await response.text()
        elif method.upper() == "POST":
            headers = {"Content-Type": "application/json"}
            async with session.post(url, json=data, headers=headers) as response:
                status = response.status
                content = await response.text()
        else:
            return {"success": False, "error": f"Unsupported method: {method}"}
        
        success = status == expected_status
        
        # Try to parse JSON response
        try:
            json_content = json.loads(content)
        except:
            json_content = {"raw_content": content[:200]}  # First 200 chars if not JSON
        
        return {
            "success": success,
            "status": status,
            "expected_status": expected_status,
            "response": json_content
        }
        
    except Exception as e:
        return {
            "success": False,
            "error": str(e)
        }

async def run_api_tests():
    """Run comprehensive API endpoint tests"""
    print("🔍 A.T.L.A.S API Endpoints Verification")
    print("=" * 50)
    
    # Define test cases
    test_cases = [
        # Core Interface
        ("GET", "/", None, 200),
        ("GET", "/api/v1/health", None, 200),
        ("GET", "/api/v1/initialization/status", None, 200),
        
        # Market Intelligence
        ("GET", f"/api/v1/quote/{TEST_SYMBOL}", None, 200),
        ("GET", "/api/v1/scan?min_strength=moderate", None, 200),
        ("GET", f"/api/v1/predicto/forecast/{TEST_SYMBOL}?days=5", None, 200),
        ("GET", f"/api/v1/market/news/{TEST_SYMBOL}", None, 200),
        ("GET", f"/api/v1/market/context/{TEST_SYMBOL}", None, 200),
        
        # Portfolio Management
        ("GET", "/api/v1/portfolio", None, 200),
        ("GET", "/api/v1/portfolio/risk-analysis", None, 200),
        ("GET", f"/api/v1/portfolio/hedging/{TEST_SYMBOL}?position_size=1000", None, 200),
        ("GET", "/api/v1/portfolio/optimization", None, 200),
        ("GET", "/api/v1/trading/pending-trades", None, 200),
        
        # POST endpoints with data
        ("POST", "/api/v1/chat", {
            "message": "Test message",
            "session_id": "test-session"
        }, 200),
        
        ("POST", "/api/v1/education", {
            "question": "What is RSI?",
            "difficulty_level": "beginner"
        }, 200),
        
        ("POST", "/api/v1/risk-assessment", {
            "symbol": TEST_SYMBOL,
            "timeframe": "1day",
            "include_risk": True
        }, 200),
        
        ("POST", "/api/v1/portfolio/auto-reinvestment", {
            "enabled": True,
            "dividend_reinvest": True,
            "profit_threshold": 100
        }, 200),
        
        ("POST", "/api/v1/trading/prepare-trade", {
            "symbol": TEST_SYMBOL,
            "action": "BUY",
            "quantity": 10,
            "stop_loss": 145.0,
            "take_profit": 155.0
        }, 200),
    ]
    
    results = []
    passed = 0
    total = len(test_cases)
    
    async with aiohttp.ClientSession() as session:
        for i, (method, endpoint, data, expected_status) in enumerate(test_cases, 1):
            print(f"\n[{i:2d}/{total}] Testing {method} {endpoint}")
            
            result = await test_endpoint(session, method, endpoint, data, expected_status)
            results.append({
                "method": method,
                "endpoint": endpoint,
                "data": data,
                "expected_status": expected_status,
                **result
            })
            
            if result["success"]:
                print(f"    ✅ PASSED (Status: {result['status']})")
                passed += 1
            else:
                print(f"    ❌ FAILED")
                if "error" in result:
                    print(f"       Error: {result['error']}")
                else:
                    print(f"       Expected: {expected_status}, Got: {result['status']}")
    
    # Summary
    print("\n" + "=" * 50)
    print("🎯 API ENDPOINTS VERIFICATION SUMMARY")
    print("=" * 50)
    
    success_rate = (passed / total) * 100
    print(f"Tests Passed: {passed}/{total}")
    print(f"Success Rate: {success_rate:.1f}%")
    
    if success_rate >= 90:
        print("🎉 EXCELLENT - All critical endpoints working!")
        status = "EXCELLENT"
    elif success_rate >= 70:
        print("✅ GOOD - Most endpoints working")
        status = "GOOD"
    elif success_rate >= 50:
        print("⚠️ FAIR - Some endpoints need attention")
        status = "FAIR"
    else:
        print("❌ POOR - Major endpoint issues")
        status = "POOR"
    
    # Detailed results
    print(f"\nDetailed Results:")
    for result in results:
        status_icon = "✅" if result["success"] else "❌"
        print(f"  {status_icon} {result['method']} {result['endpoint']}")
        if not result["success"] and "error" in result:
            print(f"     Error: {result['error']}")
    
    return success_rate >= 70

if __name__ == "__main__":
    try:
        print("🚀 Starting API Endpoints Verification...")
        print("⚠️  Note: This test requires the A.T.L.A.S server to be running on localhost:8080")
        print("   Start the server with: python atlas_server.py")
        print()
        
        success = asyncio.run(run_api_tests())
        sys.exit(0 if success else 1)
        
    except Exception as e:
        print(f"💥 Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
