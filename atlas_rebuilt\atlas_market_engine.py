"""
A.T.L.A.S Market Engine - Real-time Data and TTM Squeeze Scanner
Market data integration with Predicto API and lazy loading
"""

import asyncio
import aiohttp
import logging
from datetime import datetime, timedelta
from typing import Optional, List, Dict, Any

from config import get_api_config
from models import Quote, TTMSqueezeSignal, SignalStrength, EngineStatus, PredictoForecast

logger = logging.getLogger(__name__)


class AtlasMarketEngine:
    """
    Market data engine with real-time quotes, TTM Squeeze scanning, and Predicto integration
    """
    
    def __init__(self):
        self.alpaca_config = get_api_config("alpaca")
        self.fmp_config = get_api_config("fmp")
        self.predicto_config = get_api_config("predicto")
        
        self.status = EngineStatus.INITIALIZING
        
        # API clients (lazy loaded)
        self._alpaca_client = None
        self._fmp_session = None
        self._predicto_session = None
        
        # Data cache
        self.quote_cache = {}
        self.cache_ttl = 60  # seconds
        
        # TTM Squeeze scanner
        self.scanner_active = False
        self.scan_symbols = ["SPY", "QQQ", "IWM", "AAPL", "MSFT", "GOOGL", "AMZN", "TSLA", "NVDA", "META"]
        
        logger.info("📊 Market Engine created - API clients will load on demand")
    
    async def initialize(self):
        """Initialize market engine with connection testing"""
        try:
            # Test API connections
            await self._test_connections()
            
            self.status = EngineStatus.ACTIVE
            logger.info("✅ Market Engine initialization completed")
            
        except Exception as e:
            logger.error(f"❌ Market Engine initialization failed: {e}")
            self.status = EngineStatus.DEGRADED
            # Continue with limited functionality
    
    async def _test_connections(self):
        """Test API connections"""
        try:
            # Test FMP connection
            await self._ensure_fmp_session()
            test_quote = await self._get_fmp_quote("AAPL")
            if test_quote:
                logger.info("✅ FMP API connection tested successfully")
            
            # Test Predicto if configured
            if self.predicto_config.get("enabled"):
                await self._ensure_predicto_session()
                logger.info("✅ Predicto API connection ready")
            
        except Exception as e:
            logger.warning(f"⚠️ Some API connections failed: {e}")
    
    async def _ensure_alpaca_client(self):
        """Ensure Alpaca client is initialized"""
        if self._alpaca_client is None:
            try:
                import alpaca_trade_api as tradeapi
                self._alpaca_client = tradeapi.REST(
                    self.alpaca_config["api_key"],
                    self.alpaca_config["secret_key"],
                    self.alpaca_config["base_url"],
                    api_version='v2'
                )
                logger.info("✅ Alpaca client initialized")
            except Exception as e:
                logger.error(f"❌ Alpaca client initialization failed: {e}")
                raise
        return self._alpaca_client
    
    async def _ensure_fmp_session(self):
        """Ensure FMP HTTP session is initialized"""
        if self._fmp_session is None:
            self._fmp_session = aiohttp.ClientSession(
                timeout=aiohttp.ClientTimeout(total=30)
            )
            logger.info("✅ FMP session initialized")
        return self._fmp_session
    
    async def _ensure_predicto_session(self):
        """Ensure Predicto HTTP session is initialized"""
        if self._predicto_session is None and self.predicto_config.get("enabled"):
            self._predicto_session = aiohttp.ClientSession(
                timeout=aiohttp.ClientTimeout(total=30),
                headers={"Authorization": f"Bearer {self.predicto_config['api_key']}"}
            )
            logger.info("✅ Predicto session initialized")
        return self._predicto_session
    
    async def get_quote(self, symbol: str) -> Quote:
        """Get real-time quote with caching"""
        try:
            # Check cache first
            cache_key = f"{symbol}_{datetime.now().minute}"
            if cache_key in self.quote_cache:
                cached_time = self.quote_cache[cache_key]["timestamp"]
                if (datetime.now() - cached_time).seconds < self.cache_ttl:
                    return Quote(**self.quote_cache[cache_key])
            
            # Fetch fresh data
            quote_data = await self._get_fmp_quote(symbol)
            if quote_data:
                quote = Quote(
                    symbol=symbol,
                    price=quote_data["price"],
                    bid=quote_data.get("bid"),
                    ask=quote_data.get("ask"),
                    volume=quote_data.get("volume"),
                    timestamp=datetime.now()
                )
                
                # Cache the quote
                self.quote_cache[cache_key] = quote.dict()
                
                return quote
            else:
                raise Exception(f"No quote data available for {symbol}")
                
        except Exception as e:
            logger.error(f"Error getting quote for {symbol}: {e}")
            raise
    
    async def _get_fmp_quote(self, symbol: str) -> Optional[Dict[str, Any]]:
        """Get quote from Financial Modeling Prep"""
        try:
            session = await self._ensure_fmp_session()
            
            url = f"{self.fmp_config['base_url']}/v3/quote/{symbol}"
            params = {"apikey": self.fmp_config["api_key"]}
            
            async with session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    if data and len(data) > 0:
                        quote = data[0]
                        return {
                            "price": quote.get("price", 0.0),
                            "bid": quote.get("bid"),
                            "ask": quote.get("ask"),
                            "volume": quote.get("volume")
                        }
                else:
                    logger.error(f"FMP API error {response.status} for {symbol}")
                    
        except Exception as e:
            logger.error(f"FMP quote error for {symbol}: {e}")
        
        return None
    
    async def scan_market(self, min_strength: str = "moderate") -> List[TTMSqueezeSignal]:
        """TTM Squeeze market scanner"""
        try:
            signals = []
            
            for symbol in self.scan_symbols:
                try:
                    # Get quote data
                    quote = await self.get_quote(symbol)
                    
                    # Calculate TTM Squeeze (simplified)
                    signal = await self._calculate_ttm_squeeze(symbol, quote.price)
                    
                    if signal and self._meets_strength_criteria(signal.signal_strength, min_strength):
                        signals.append(signal)
                        
                except Exception as e:
                    logger.warning(f"Scan error for {symbol}: {e}")
                    continue
            
            # Sort by signal strength
            signals.sort(key=lambda x: self._strength_to_numeric(x.signal_strength), reverse=True)
            
            return signals[:20]  # Return top 20 signals
            
        except Exception as e:
            logger.error(f"Market scan error: {e}")
            return []
    
    async def _calculate_ttm_squeeze(self, symbol: str, price: float) -> Optional[TTMSqueezeSignal]:
        """Calculate real TTM Squeeze signal using Bollinger Bands and Keltner Channels"""
        try:
            # Get historical data for technical analysis
            historical_data = await self._get_historical_data(symbol, period="1d", limit=50)
            if not historical_data or len(historical_data) < 20:
                logger.warning(f"Insufficient historical data for TTM Squeeze calculation: {symbol}")
                return None

            import pandas as pd
            import numpy as np

            # Convert to DataFrame for technical analysis
            df = pd.DataFrame(historical_data)
            df['close'] = df['close'].astype(float)
            df['high'] = df['high'].astype(float)
            df['low'] = df['low'].astype(float)
            df['volume'] = df['volume'].astype(float)

            # Calculate Bollinger Bands (20-period, 2 std dev)
            bb_period = 20
            bb_std = 2.0
            df['bb_middle'] = df['close'].rolling(window=bb_period).mean()
            bb_std_dev = df['close'].rolling(window=bb_period).std()
            df['bb_upper'] = df['bb_middle'] + (bb_std_dev * bb_std)
            df['bb_lower'] = df['bb_middle'] - (bb_std_dev * bb_std)

            # Calculate Keltner Channels (20-period, 1.5 multiplier)
            kc_period = 20
            kc_multiplier = 1.5
            df['kc_middle'] = df['close'].rolling(window=kc_period).mean()

            # True Range calculation for Keltner Channels
            df['tr1'] = df['high'] - df['low']
            df['tr2'] = abs(df['high'] - df['close'].shift(1))
            df['tr3'] = abs(df['low'] - df['close'].shift(1))
            df['true_range'] = df[['tr1', 'tr2', 'tr3']].max(axis=1)
            df['atr'] = df['true_range'].rolling(window=kc_period).mean()

            df['kc_upper'] = df['kc_middle'] + (df['atr'] * kc_multiplier)
            df['kc_lower'] = df['kc_middle'] - (df['atr'] * kc_multiplier)

            # TTM Squeeze condition: Bollinger Bands inside Keltner Channels
            df['squeeze_active'] = (df['bb_upper'] <= df['kc_upper']) & (df['bb_lower'] >= df['kc_lower'])

            # Momentum calculation (Linear Regression of close prices)
            momentum_period = 12
            df['momentum'] = 0.0

            for i in range(momentum_period, len(df)):
                y = df['close'].iloc[i-momentum_period:i].values
                x = np.arange(len(y))
                if len(y) > 0:
                    # Linear regression slope as momentum
                    slope = np.polyfit(x, y, 1)[0]
                    df.loc[df.index[i], 'momentum'] = slope

            # Get current values
            current_squeeze = df['squeeze_active'].iloc[-1]
            current_momentum = df['momentum'].iloc[-1]
            previous_momentum = df['momentum'].iloc[-2] if len(df) > 1 else 0

            # Calculate histogram value (momentum change)
            histogram_value = current_momentum - previous_momentum

            # Determine signal strength based on momentum magnitude and squeeze status
            momentum_magnitude = abs(current_momentum)

            if current_squeeze:
                # During squeeze, lower threshold for signals
                if momentum_magnitude > 2.0:
                    strength = SignalStrength.VERY_STRONG
                elif momentum_magnitude > 1.5:
                    strength = SignalStrength.STRONG
                elif momentum_magnitude > 1.0:
                    strength = SignalStrength.MODERATE
                elif momentum_magnitude > 0.5:
                    strength = SignalStrength.WEAK
                else:
                    strength = SignalStrength.VERY_WEAK
            else:
                # Post-squeeze, higher threshold
                if momentum_magnitude > 3.0:
                    strength = SignalStrength.VERY_STRONG
                elif momentum_magnitude > 2.0:
                    strength = SignalStrength.STRONG
                elif momentum_magnitude > 1.5:
                    strength = SignalStrength.MODERATE
                elif momentum_magnitude > 1.0:
                    strength = SignalStrength.WEAK
                else:
                    strength = SignalStrength.VERY_WEAK

            # Determine momentum direction
            if current_momentum > 0.3:
                momentum_direction = "bullish"
            elif current_momentum < -0.3:
                momentum_direction = "bearish"
            else:
                momentum_direction = "neutral"

            # Calculate confidence based on multiple factors
            confidence = 0.5  # Base confidence

            # Higher confidence during squeeze breakouts
            if not current_squeeze and df['squeeze_active'].iloc[-2]:
                confidence += 0.2

            # Higher confidence with strong momentum
            if momentum_magnitude > 1.5:
                confidence += 0.2

            # Higher confidence with volume confirmation
            avg_volume = df['volume'].rolling(window=10).mean().iloc[-1]
            current_volume = df['volume'].iloc[-1]
            if current_volume > avg_volume * 1.2:
                confidence += 0.1

            confidence = min(confidence, 0.95)  # Cap at 95%

            # Calculate stop loss and target using ATR
            current_atr = df['atr'].iloc[-1]
            atr_multiplier = 2.0

            if momentum_direction == "bullish":
                stop_loss = price - (current_atr * atr_multiplier)
                target_price = price + (current_atr * atr_multiplier * 1.5)  # 1.5:1 R/R
            elif momentum_direction == "bearish":
                stop_loss = price + (current_atr * atr_multiplier)
                target_price = price - (current_atr * atr_multiplier * 1.5)
            else:
                stop_loss = price * 0.98
                target_price = price * 1.02

            return TTMSqueezeSignal(
                symbol=symbol,
                signal_strength=strength,
                histogram_value=float(histogram_value),
                squeeze_active=bool(current_squeeze),
                momentum_direction=momentum_direction,
                confidence=float(confidence),
                entry_price=price,
                stop_loss=float(stop_loss),
                target_price=float(target_price),
                timestamp=datetime.now()
            )

        except Exception as e:
            logger.error(f"TTM Squeeze calculation error for {symbol}: {e}")
            # Fallback to basic calculation if technical analysis fails
            return await self._basic_ttm_fallback(symbol, price)

    async def _get_historical_data(self, symbol: str, period: str = "1d", limit: int = 50) -> List[Dict]:
        """Get historical price data for technical analysis"""
        try:
            if not self.fmp_client:
                logger.warning("FMP client not available for historical data")
                return []

            # Use FMP API to get historical data
            url = f"https://financialmodelingprep.com/api/v3/historical-price-full/{symbol}"
            params = {
                'apikey': self.config.get('fmp_api_key'),
                'timeseries': limit
            }

            async with self.fmp_client.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    if 'historical' in data:
                        # Convert FMP format to our format
                        historical = []
                        for item in data['historical'][:limit]:
                            historical.append({
                                'close': item['close'],
                                'high': item['high'],
                                'low': item['low'],
                                'volume': item['volume'],
                                'date': item['date']
                            })
                        return list(reversed(historical))  # Oldest first for technical analysis

                logger.warning(f"No historical data available for {symbol}")
                return []

        except Exception as e:
            logger.error(f"Error fetching historical data for {symbol}: {e}")
            return []

    async def _basic_ttm_fallback(self, symbol: str, price: float) -> TTMSqueezeSignal:
        """Fallback TTM Squeeze calculation when full technical analysis fails"""
        try:
            # Basic momentum estimation using recent price action
            recent_quotes = []
            for i in range(5):  # Get last 5 quotes if available
                cache_key = f"{symbol}_{i}"
                if cache_key in self.quote_cache:
                    recent_quotes.append(self.quote_cache[cache_key]['price'])

            if len(recent_quotes) >= 3:
                # Simple momentum: current vs average of recent prices
                avg_recent = sum(recent_quotes[:-1]) / len(recent_quotes[:-1])
                momentum = (price - avg_recent) / avg_recent * 100

                # Estimate signal strength
                if abs(momentum) > 3.0:
                    strength = SignalStrength.STRONG
                elif abs(momentum) > 2.0:
                    strength = SignalStrength.MODERATE
                elif abs(momentum) > 1.0:
                    strength = SignalStrength.WEAK
                else:
                    strength = SignalStrength.VERY_WEAK

                momentum_direction = "bullish" if momentum > 0.5 else "bearish" if momentum < -0.5 else "neutral"
                confidence = min(0.6, abs(momentum) / 5.0)  # Lower confidence for fallback

            else:
                # Minimal fallback
                strength = SignalStrength.WEAK
                momentum_direction = "neutral"
                confidence = 0.3
                momentum = 0.0

            return TTMSqueezeSignal(
                symbol=symbol,
                signal_strength=strength,
                histogram_value=momentum,
                squeeze_active=False,  # Conservative assumption
                momentum_direction=momentum_direction,
                confidence=confidence,
                entry_price=price,
                stop_loss=price * 0.98 if momentum_direction == "bullish" else price * 1.02,
                target_price=price * 1.03 if momentum_direction == "bullish" else price * 0.97,
                timestamp=datetime.now()
            )

        except Exception as e:
            logger.error(f"Fallback TTM calculation error for {symbol}: {e}")
            # Ultimate fallback
            return TTMSqueezeSignal(
                symbol=symbol,
                signal_strength=SignalStrength.VERY_WEAK,
                histogram_value=0.0,
                squeeze_active=False,
                momentum_direction="neutral",
                confidence=0.2,
                entry_price=price,
                stop_loss=price * 0.98,
                target_price=price * 1.02,
                timestamp=datetime.now()
            )

    def _meets_strength_criteria(self, signal_strength: SignalStrength, min_strength: str) -> bool:
        """Check if signal meets minimum strength criteria"""
        strength_order = {
            "very_weak": 1,
            "weak": 2,
            "moderate": 3,
            "strong": 4,
            "very_strong": 5
        }
        
        signal_value = strength_order.get(signal_strength.value, 0)
        min_value = strength_order.get(min_strength, 3)
        
        return signal_value >= min_value
    
    def _strength_to_numeric(self, strength: SignalStrength) -> int:
        """Convert signal strength to numeric value for sorting"""
        strength_map = {
            SignalStrength.VERY_WEAK: 1,
            SignalStrength.WEAK: 2,
            SignalStrength.MODERATE: 3,
            SignalStrength.STRONG: 4,
            SignalStrength.VERY_STRONG: 5
        }
        return strength_map.get(strength, 0)
    
    async def get_predicto_forecast(self, symbol: str, days: int = 5) -> Optional[PredictoForecast]:
        """Get Predicto AI forecast"""
        try:
            if not self.predicto_config.get("enabled"):
                return None
            
            session = await self._ensure_predicto_session()
            if not session:
                return None
            
            url = f"{self.predicto_config['base_url']}/forecast"
            data = {
                "symbol": symbol,
                "days": days,
                "model": "advanced"
            }
            
            async with session.post(url, json=data) as response:
                if response.status == 200:
                    result = await response.json()
                    
                    return PredictoForecast(
                        symbol=symbol,
                        forecast_days=days,
                        predicted_price=result.get("predicted_price", 0.0),
                        confidence=result.get("confidence", 0.5),
                        price_targets=result.get("price_targets"),
                        risk_factors=result.get("risk_factors"),
                        sentiment_score=result.get("sentiment_score"),
                        timestamp=datetime.now()
                    )
                else:
                    logger.warning(f"Predicto API error {response.status} for {symbol}")
                    
        except Exception as e:
            logger.error(f"Predicto forecast error for {symbol}: {e}")
        
        return None
    
    async def cleanup(self):
        """Cleanup market engine resources"""
        try:
            # Close HTTP sessions
            if self._fmp_session:
                await self._fmp_session.close()
            
            if self._predicto_session:
                await self._predicto_session.close()
            
            # Clear cache
            self.quote_cache.clear()
            
            logger.info("✅ Market Engine cleanup completed")
            
        except Exception as e:
            logger.error(f"Error during market engine cleanup: {e}")
