# 🚀 A.T.L.A.S AI Trading System v4.0

**Advanced Trading & Learning Analysis System**  
*Production-Ready Non-Blocking Architecture*

## 🎯 Overview

A.T.L.A.S is a sophisticated AI-powered trading system that combines real-time market analysis, multi-agent AI decision-making, and educational capabilities in a ChatGPT-style conversational interface. This v4.0 rebuild features a production-ready, non-blocking architecture that eliminates initialization deadlocks while maintaining complete functionality.

## ✨ Key Features

### 🤖 **Multi-Agent AI System**
- **Technical Analysis Agent**: Chart patterns, indicators, trend analysis
- **Risk Management Agent**: Position sizing, stop-loss calculation, portfolio risk
- **Sentiment Analysis Agent**: News sentiment, market psychology
- **Execution Agent**: Order timing, market impact optimization
- **Consensus Decision Making**: Weighted voting with confidence scoring

### 📊 **Real-Time Market Intelligence**
- **TTM Squeeze Scanner**: 5-star signal rating system with live monitoring
- **Predicto AI Integration**: Enhanced market forecasting and predictions
- **Real-Time Quotes**: FMP API integration with intelligent caching
- **Market Context Analysis**: VIX levels, sector rotation, news sentiment

### 🛡️ **AI-Enhanced Risk Management**
- **Dynamic Stop-Loss**: Technical, volatility, and support-based calculations
- **Position Sizing**: Volatility-adjusted with account risk limits
- **Circuit Breakers**: Daily loss limits and portfolio risk monitoring
- **Safety Guardrails**: Automated risk assessment and warnings

### 📚 **Educational RAG System**
- **Trading Literature**: Vector database with classic trading books
- **Adaptive Learning**: Difficulty-level based responses
- **Q&A System**: ChromaDB-powered knowledge retrieval
- **Mentor-Style Communication**: Beginner-friendly explanations

### 💼 **Paper Trading Engine**
- **Order Management**: Market, limit, stop, and stop-limit orders
- **Portfolio Tracking**: Real-time P&L, positions, performance metrics
- **Alpaca Integration**: Professional-grade trading infrastructure
- **Risk Monitoring**: Real-time position and portfolio risk assessment

## 🏗️ Architecture

### **Non-Blocking Startup Pattern**
```
FastAPI Server (starts in <3 seconds)
    ↓
Background Initialization (asyncio.create_task)
    ↓
Lazy Component Loading (on-demand)
    ↓
Graceful Degradation (fallback responses)
```

### **File Structure (12 Core Files)**
```
atlas_rebuilt/
├── atlas_server.py           # Non-blocking FastAPI server
├── atlas_orchestrator.py     # Lazy-loading coordinator  
├── atlas_ai_engine.py        # Multi-agent AI system
├── atlas_market_engine.py    # Real-time data & TTM Squeeze
├── atlas_trading_engine.py   # Paper trading & orders
├── atlas_risk_engine.py      # AI-enhanced risk management
├── atlas_education_engine.py # RAG system with trading books
├── atlas_database_manager.py # Async SQLite operations
├── config.py                 # Environment configuration
├── models.py                 # Pydantic data models
├── atlas_interface.html      # Single-page web interface
└── requirements.txt          # Dependencies
```

## 🚀 Quick Start

### **1. Installation**
```bash
# Clone or download the atlas_rebuilt directory
cd atlas_rebuilt

# Install dependencies
pip install -r requirements.txt
```

### **2. Configuration**
Ensure your `.env` file contains:
```env
# Alpaca Trading API (Paper Trading)
APCA_API_KEY_ID=PKI0KNC8HXZURYRA4OMC
APCA_API_SECRET_KEY=7ydtObOUVC22xP2IJbEhetmKrvec7N9owdcor0hn

# Financial Modeling Prep API
FMP_API_KEY=K63wnAbUDHNPICjzeq3JcEG1vi8Q2oz7

# OpenAI API
OPENAI_API_KEY=********************************************************************************************************************************************************************

# Predicto AI (Enhanced Predictions)
PREDICTO_API_KEY=VZ19mf7DvVovUKW0E7PTYmzrJkQjkC5N5fMcWwOsglWPFzhSQPU8m77cb3d3k760
```

### **3. Start the System**
```bash
# Option 1: Direct startup
python atlas_server.py

# Option 2: Comprehensive startup with validation
python start_atlas.py

# Option 3: Test the system
python test_system.py
```

### **4. Access the System**
- **Web Interface**: http://localhost:8080
- **API Documentation**: http://localhost:8080/docs
- **Health Check**: http://localhost:8080/api/v1/health

## 📡 API Endpoints

### **Core Endpoints**
- `GET /api/v1/health` - System health and initialization status
- `POST /api/v1/chat` - Main conversational interface
- `GET /api/v1/initialization/status` - Detailed initialization progress

### **Market Data**
- `GET /api/v1/quote/{symbol}` - Real-time market quotes
- `GET /api/v1/scan` - TTM Squeeze market scanner
- `GET /api/v1/predicto/forecast/{symbol}` - Predicto AI predictions

### **Trading & Risk**
- `GET /api/v1/portfolio` - Portfolio summary and positions
- `POST /api/v1/risk-assessment` - AI-enhanced risk analysis

### **Education**
- `POST /api/v1/education` - RAG-based educational queries

## 🎯 Usage Examples

### **Chat Interface**
```javascript
// Send a message to A.T.L.A.S
const response = await fetch('/api/v1/chat', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
        message: "What's your analysis of AAPL?",
        session_id: "user-session-123"
    })
});
```

### **Market Scanning**
```javascript
// Get TTM Squeeze signals
const signals = await fetch('/api/v1/scan?min_strength=strong');
const data = await signals.json();
console.log(`Found ${data.signals.length} strong signals`);
```

### **Risk Assessment**
```javascript
// Assess risk for a trade
const assessment = await fetch('/api/v1/risk-assessment', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
        symbol: "TSLA",
        timeframe: "1day",
        include_risk: true
    })
});
```

## 🔧 Advanced Configuration

### **Environment Variables**
```env
# Application Settings
ENVIRONMENT=development
DEBUG=true
LOG_LEVEL=INFO
PORT=8080

# Trading Configuration
PAPER_TRADING=true
DEFAULT_RISK_PERCENT=2.0
MAX_POSITIONS=10

# Performance Settings
API_TIMEOUT=30
CACHE_TTL=300
STARTUP_TIMEOUT=60
```

### **Customization**
- **Risk Parameters**: Modify `atlas_risk_engine.py` for custom risk rules
- **Trading Strategies**: Extend `atlas_ai_engine.py` for new analysis methods
- **UI Styling**: Customize `atlas_interface.html` for branding
- **Educational Content**: Add books to `atlas_education_engine.py`

## 🛡️ Security & Safety

### **Built-in Safety Features**
- **Paper Trading Only**: No real money at risk
- **Circuit Breakers**: Automatic trading halts on excessive losses
- **Input Validation**: Comprehensive request validation
- **Error Handling**: Graceful degradation on failures
- **Rate Limiting**: API request throttling

### **Risk Management**
- **Position Limits**: Maximum 20% of portfolio per position
- **Daily Loss Limits**: 3% maximum daily loss
- **Stop-Loss Automation**: AI-calculated stop prices
- **Portfolio Risk Monitoring**: Real-time risk assessment

## 📊 Performance

### **Startup Performance**
- **Server Response**: <3 seconds to first request
- **Health Check**: <1 second response time
- **Full Initialization**: <60 seconds background loading

### **Runtime Performance**
- **Chat Responses**: <10 seconds with AI analysis
- **Market Data**: <2 seconds with caching
- **Risk Assessment**: <5 seconds comprehensive analysis

## 🧪 Testing

### **Automated Testing**
```bash
# Run comprehensive system test
python test_system.py

# Test individual components
python -m pytest tests/ -v
```

### **Manual Testing**
1. **Health Check**: Verify all engines report "active"
2. **Chat Interface**: Test conversational responses
3. **Market Data**: Verify real-time quotes
4. **Risk Analysis**: Test position sizing calculations

## 🤝 Contributing

### **Development Setup**
```bash
# Install development dependencies
pip install -r requirements.txt
pip install pytest black flake8

# Run code formatting
black .

# Run linting
flake8 .
```

### **Architecture Guidelines**
- **Lazy Loading**: All heavy operations must be lazy-loaded
- **Error Handling**: Every function must handle failures gracefully
- **Async Operations**: Use async/await for I/O operations
- **Type Hints**: All functions must have proper type annotations

## 📄 License

This project is for educational and research purposes. Not intended for live trading without proper risk management and regulatory compliance.

## 🆘 Support

### **Common Issues**
1. **Server won't start**: Check environment variables in `.env`
2. **API errors**: Verify API keys are valid and have proper permissions
3. **Slow responses**: Check network connectivity and API rate limits

### **Getting Help**
- Check the health endpoint: `/api/v1/health`
- Review logs in `atlas.log` and `atlas_startup.log`
- Test individual components with `test_system.py`

---

**A.T.L.A.S v4.0** - *Where AI meets Trading Intelligence* 🚀
