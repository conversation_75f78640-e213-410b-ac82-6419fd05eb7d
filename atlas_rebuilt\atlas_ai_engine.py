"""
A.T.L.A.S AI Engine - Multi-Agent System with Background Loading
Conversational AI with chain-of-thought analysis and lazy ML model loading
"""

import asyncio
import logging
import json
from datetime import datetime
from typing import Optional, List, Dict, Any, Union

from config import get_api_config
from models import AIResponse, EngineStatus

logger = logging.getLogger(__name__)


class AtlasAIEngine:
    """
    Advanced AI engine with multi-agent system and lazy loading
    """
    
    def __init__(self):
        self.config = get_api_config("openai")
        self.status = EngineStatus.INITIALIZING
        
        # OpenAI client (lazy loaded)
        self._openai_client = None
        self._client_lock = asyncio.Lock()
        
        # Multi-agent system
        self.agents = {
            "technical": TechnicalAnalysisAgent(),
            "risk": RiskManagementAgent(),
            "sentiment": SentimentAnalysisAgent(),
            "execution": ExecutionAgent()
        }
        
        # Conversation context
        self.conversation_memory = {}
        self.user_profiles = {}
        
        logger.info("🧠 AI Engine created - OpenAI client will load on demand")
    
    async def initialize(self):
        """Initialize AI engine with lazy loading"""
        try:
            # Test OpenAI connection
            await self._ensure_openai_client()
            
            # Initialize agents
            for name, agent in self.agents.items():
                await agent.initialize()
                logger.info(f"✅ {name.title()} agent initialized")
            
            self.status = EngineStatus.ACTIVE
            logger.info("✅ AI Engine initialization completed")
            
        except Exception as e:
            logger.error(f"❌ AI Engine initialization failed: {e}")
            self.status = EngineStatus.FAILED
            # Continue with degraded functionality
    
    async def _ensure_openai_client(self):
        """Ensure OpenAI client is initialized"""
        if self._openai_client is None:
            async with self._client_lock:
                if self._openai_client is None:
                    try:
                        import openai
                        self._openai_client = openai.AsyncOpenAI(
                            api_key=self.config["api_key"]
                        )
                        
                        # Test connection
                        await self._openai_client.models.list()
                        logger.info("✅ OpenAI client initialized and tested")
                        
                    except Exception as e:
                        logger.error(f"❌ OpenAI client initialization failed: {e}")
                        raise
        
        return self._openai_client
    
    async def process_message(self, message: str, session_id: Optional[str], orchestrator) -> AIResponse:
        """Process user message with multi-agent analysis"""
        try:
            # Ensure OpenAI client is available
            client = await self._ensure_openai_client()
            
            # Analyze message intent
            intent = await self._analyze_intent(message)
            
            # Route to appropriate processing
            if intent["type"] == "trading_analysis":
                return await self._process_trading_analysis(message, session_id, orchestrator)
            elif intent["type"] == "education":
                return await self._process_education_query(message, session_id, orchestrator)
            elif intent["type"] == "portfolio":
                return await self._process_portfolio_query(message, session_id, orchestrator)
            elif intent["type"] == "market_data":
                return await self._process_market_query(message, session_id, orchestrator)
            else:
                return await self._process_general_chat(message, session_id, orchestrator)
                
        except Exception as e:
            logger.error(f"Error processing message: {e}")
            return AIResponse(
                response="I encountered an error processing your message. Please try again.",
                type="error",
                confidence=0.0,
                context={"error": str(e)}
            )
    
    async def _analyze_intent(self, message: str) -> Dict[str, Any]:
        """Analyze user message intent"""
        try:
            client = await self._ensure_openai_client()
            
            response = await client.chat.completions.create(
                model=self.config["model"],
                messages=[
                    {
                        "role": "system",
                        "content": """You are an intent classifier for a trading AI system. 
                        Classify the user's message into one of these categories:
                        - trading_analysis: Questions about stocks, technical analysis, trading signals
                        - education: Questions about trading concepts, learning requests
                        - portfolio: Questions about positions, P&L, account status
                        - market_data: Requests for quotes, market information
                        - general_chat: Greetings, general conversation
                        
                        Respond with JSON: {"type": "category", "confidence": 0.0-1.0, "entities": []}"""
                    },
                    {"role": "user", "content": message}
                ],
                temperature=0.1,
                max_tokens=200
            )
            
            result = json.loads(response.choices[0].message.content)
            return result
            
        except Exception as e:
            logger.error(f"Intent analysis failed: {e}")
            return {"type": "general_chat", "confidence": 0.5, "entities": []}
    
    async def _process_trading_analysis(self, message: str, session_id: str, orchestrator) -> AIResponse:
        """Process trading analysis request with multi-agent system"""
        try:
            # Get multi-agent consensus
            agent_responses = {}
            
            # Technical analysis
            if "technical" in self.agents:
                agent_responses["technical"] = await self.agents["technical"].analyze(message, orchestrator)
            
            # Risk assessment
            if "risk" in self.agents:
                agent_responses["risk"] = await self.agents["risk"].analyze(message, orchestrator)
            
            # Sentiment analysis
            if "sentiment" in self.agents:
                agent_responses["sentiment"] = await self.agents["sentiment"].analyze(message, orchestrator)

            # Execution timing analysis
            if "execution" in self.agents:
                agent_responses["execution"] = await self.agents["execution"].analyze(message, orchestrator)

            # Generate consensus response with chain-of-thought
            consensus = await self._generate_consensus(agent_responses, message)

            # Add chain-of-thought reasoning
            cot_analysis = await self._generate_chain_of_thought(agent_responses, consensus, message)

            return AIResponse(
                response=cot_analysis["response"],
                type="trading_analysis",
                confidence=cot_analysis["confidence"],
                context={
                    "agent_responses": agent_responses,
                    "consensus_reasoning": consensus["reasoning"],
                    "chain_of_thought": cot_analysis["reasoning_steps"]
                }
            )
            
        except Exception as e:
            logger.error(f"Trading analysis failed: {e}")
            return await self._fallback_response(message, "trading_analysis")
    
    async def _process_education_query(self, message: str, session_id: str, orchestrator) -> AIResponse:
        """Process educational query"""
        try:
            # Use education engine if available
            if hasattr(orchestrator, '_education_engine') and orchestrator._education_engine:
                education_response = await orchestrator._education_engine.process_query({
                    "question": message,
                    "difficulty_level": "beginner"
                })
                return education_response
            
            # Fallback to AI response
            return await self._generate_educational_response(message)
            
        except Exception as e:
            logger.error(f"Education query failed: {e}")
            return await self._fallback_response(message, "education")
    
    async def _process_portfolio_query(self, message: str, session_id: str, orchestrator) -> AIResponse:
        """Process portfolio-related query"""
        try:
            # Get portfolio data if trading engine is available
            portfolio_data = None
            if hasattr(orchestrator, '_trading_engine') and orchestrator._trading_engine:
                portfolio_data = await orchestrator._trading_engine.get_portfolio_summary()
            
            # Generate response with portfolio context
            return await self._generate_portfolio_response(message, portfolio_data)
            
        except Exception as e:
            logger.error(f"Portfolio query failed: {e}")
            return await self._fallback_response(message, "portfolio")
    
    async def _process_market_query(self, message: str, session_id: str, orchestrator) -> AIResponse:
        """Process market data query"""
        try:
            # Extract symbol from message
            symbol = self._extract_symbol(message)
            
            # Get market data if market engine is available
            market_data = None
            if symbol and hasattr(orchestrator, '_market_engine') and orchestrator._market_engine:
                market_data = await orchestrator._market_engine.get_quote(symbol)
            
            # Generate response with market context
            return await self._generate_market_response(message, symbol, market_data)
            
        except Exception as e:
            logger.error(f"Market query failed: {e}")
            return await self._fallback_response(message, "market_data")
    
    async def _process_general_chat(self, message: str, session_id: str, orchestrator) -> AIResponse:
        """Process general conversation"""
        try:
            client = await self._ensure_openai_client()
            
            # Get conversation context
            context = self.conversation_memory.get(session_id, [])
            
            # Build conversation messages
            messages = [
                {
                    "role": "system",
                    "content": """You are A.T.L.A.S, an advanced AI trading assistant. You are knowledgeable, 
                    helpful, and mentor-like in your communication style. You help users with trading, 
                    market analysis, and financial education. Keep responses conversational and engaging."""
                }
            ]
            
            # Add recent context
            for ctx in context[-5:]:  # Last 5 messages
                messages.append({"role": "user", "content": ctx["message"]})
                messages.append({"role": "assistant", "content": ctx["response"]})
            
            # Add current message
            messages.append({"role": "user", "content": message})
            
            response = await client.chat.completions.create(
                model=self.config["model"],
                messages=messages,
                temperature=self.config["temperature"],
                max_tokens=500
            )
            
            ai_response = response.choices[0].message.content
            
            # Update conversation memory
            if session_id not in self.conversation_memory:
                self.conversation_memory[session_id] = []
            
            self.conversation_memory[session_id].append({
                "message": message,
                "response": ai_response,
                "timestamp": datetime.now()
            })
            
            # Keep only recent messages
            if len(self.conversation_memory[session_id]) > 20:
                self.conversation_memory[session_id] = self.conversation_memory[session_id][-20:]
            
            return AIResponse(
                response=ai_response,
                type="chat",
                confidence=0.8
            )
            
        except Exception as e:
            logger.error(f"General chat failed: {e}")
            return await self._fallback_response(message, "chat")
    
    async def _fallback_response(self, message: str, response_type: str) -> AIResponse:
        """Generate fallback response when AI is unavailable"""
        fallback_responses = {
            "trading_analysis": "I'm currently unable to perform detailed trading analysis. Please try again in a moment.",
            "education": "I'm currently unable to access educational content. Please try again in a moment.",
            "portfolio": "I'm currently unable to access portfolio data. Please try again in a moment.",
            "market_data": "I'm currently unable to fetch market data. Please try again in a moment.",
            "chat": "Hello! I'm A.T.L.A.S, your AI trading assistant. I'm currently initializing my systems. Please try again in a moment."
        }
        
        return AIResponse(
            response=fallback_responses.get(response_type, "I'm currently initializing. Please try again in a moment."),
            type="fallback",
            confidence=0.3
        )
    
    def _extract_symbol(self, message: str) -> Optional[str]:
        """Extract stock symbol from message"""
        import re
        # Simple regex to find stock symbols (1-5 uppercase letters)
        match = re.search(r'\b[A-Z]{1,5}\b', message.upper())
        return match.group() if match else None
    
    async def cleanup(self):
        """Cleanup AI engine resources"""
        try:
            # Cleanup agents
            for agent in self.agents.values():
                if hasattr(agent, 'cleanup'):
                    await agent.cleanup()
            
            # Clear memory
            self.conversation_memory.clear()
            
            logger.info("✅ AI Engine cleanup completed")
            
        except Exception as e:
            logger.error(f"Error during AI engine cleanup: {e}")


# Multi-Agent System Implementation
class TechnicalAnalysisAgent:
    """Technical Analysis Agent - 35% weight in consensus"""

    def __init__(self):
        self.weight = 0.35
        self.initialized = False

    async def initialize(self):
        """Initialize technical analysis capabilities"""
        try:
            self.initialized = True
            logger.info("Technical Analysis Agent initialized")
        except Exception as e:
            logger.error(f"Technical Analysis Agent initialization failed: {e}")

    async def analyze(self, message: str, orchestrator) -> Dict[str, Any]:
        """Perform technical analysis on trading requests"""
        try:
            # Extract symbol from message
            symbol = self._extract_symbol(message)
            if not symbol:
                return {
                    "analysis": "No valid symbol found for technical analysis",
                    "confidence": 0.1,
                    "recommendation": "neutral",
                    "reasoning": "Cannot perform technical analysis without a valid symbol"
                }

            # Get market data from orchestrator
            market_engine = getattr(orchestrator, 'market_engine', None)
            if not market_engine:
                return {
                    "analysis": "Market data unavailable for technical analysis",
                    "confidence": 0.2,
                    "recommendation": "neutral",
                    "reasoning": "Market engine not available"
                }

            # Get current quote and TTM Squeeze signal
            quote = await market_engine.get_quote(symbol)
            ttm_signal = await market_engine._calculate_ttm_squeeze(symbol, quote.price if quote else 100.0)

            if not quote or not ttm_signal:
                return {
                    "analysis": f"Unable to get technical data for {symbol}",
                    "confidence": 0.2,
                    "recommendation": "neutral",
                    "reasoning": "Insufficient market data"
                }

            # Perform technical analysis
            analysis_result = self._analyze_technical_indicators(quote, ttm_signal)

            return {
                "analysis": analysis_result["summary"],
                "confidence": analysis_result["confidence"],
                "recommendation": analysis_result["recommendation"],
                "reasoning": analysis_result["reasoning"],
                "technical_details": analysis_result["details"]
            }

        except Exception as e:
            logger.error(f"Technical Analysis Agent error: {e}")
            return {
                "analysis": "Technical analysis failed due to system error",
                "confidence": 0.1,
                "recommendation": "neutral",
                "reasoning": f"Error: {str(e)}"
            }

    def _extract_symbol(self, message: str) -> Optional[str]:
        """Extract stock symbol from user message"""
        import re

        # Common patterns for stock symbols
        patterns = [
            r'\b([A-Z]{1,5})\b',  # 1-5 uppercase letters
            r'\$([A-Z]{1,5})\b',  # $SYMBOL format
        ]

        for pattern in patterns:
            matches = re.findall(pattern, message.upper())
            if matches:
                # Return first valid-looking symbol
                for match in matches:
                    if len(match) >= 1 and len(match) <= 5:
                        return match

        return None

    def _analyze_technical_indicators(self, quote, ttm_signal) -> Dict[str, Any]:
        """Analyze technical indicators and generate recommendation"""
        try:
            confidence = 0.5  # Base confidence
            recommendation = "neutral"
            reasoning_parts = []
            details = {}

            # TTM Squeeze Analysis
            if ttm_signal:
                details["ttm_squeeze"] = {
                    "active": ttm_signal.squeeze_active,
                    "momentum": ttm_signal.momentum_direction,
                    "strength": ttm_signal.signal_strength.value,
                    "histogram": ttm_signal.histogram_value
                }

                if ttm_signal.squeeze_active:
                    reasoning_parts.append("TTM Squeeze is active - consolidation phase, awaiting breakout")
                    confidence += 0.1
                else:
                    if ttm_signal.momentum_direction == "bullish":
                        recommendation = "buy"
                        reasoning_parts.append(f"TTM Squeeze fired bullish with {ttm_signal.signal_strength.value} strength")
                        confidence += 0.2
                    elif ttm_signal.momentum_direction == "bearish":
                        recommendation = "sell"
                        reasoning_parts.append(f"TTM Squeeze fired bearish with {ttm_signal.signal_strength.value} strength")
                        confidence += 0.2

                # Adjust confidence based on signal strength
                strength_multiplier = {
                    "very_strong": 0.3,
                    "strong": 0.2,
                    "moderate": 0.1,
                    "weak": 0.05,
                    "very_weak": 0.0
                }
                confidence += strength_multiplier.get(ttm_signal.signal_strength.value, 0.0)

            # Price action analysis
            if quote:
                details["price_action"] = {
                    "current_price": quote.price,
                    "change_percent": quote.change_percent,
                    "volume": quote.volume
                }

                # Strong price movement
                if abs(quote.change_percent) > 3.0:
                    if quote.change_percent > 0:
                        reasoning_parts.append(f"Strong upward momentum (+{quote.change_percent:.1f}%)")
                        if recommendation == "neutral":
                            recommendation = "buy"
                    else:
                        reasoning_parts.append(f"Strong downward momentum ({quote.change_percent:.1f}%)")
                        if recommendation == "neutral":
                            recommendation = "sell"
                    confidence += 0.15

                # Volume confirmation
                if quote.volume and quote.volume > 1000000:  # High volume threshold
                    reasoning_parts.append("High volume confirms price movement")
                    confidence += 0.1

            # Cap confidence at reasonable levels
            confidence = min(confidence, 0.9)

            # Generate summary
            if recommendation == "buy":
                summary = f"Technical analysis suggests BULLISH outlook for {quote.symbol if quote else 'symbol'}"
            elif recommendation == "sell":
                summary = f"Technical analysis suggests BEARISH outlook for {quote.symbol if quote else 'symbol'}"
            else:
                summary = f"Technical analysis shows NEUTRAL outlook for {quote.symbol if quote else 'symbol'}"

            return {
                "summary": summary,
                "confidence": confidence,
                "recommendation": recommendation,
                "reasoning": " | ".join(reasoning_parts) if reasoning_parts else "Limited technical signals available",
                "details": details
            }

        except Exception as e:
            logger.error(f"Technical indicator analysis error: {e}")
            return {
                "summary": "Technical analysis incomplete",
                "confidence": 0.2,
                "recommendation": "neutral",
                "reasoning": f"Analysis error: {str(e)}",
                "details": {}
            }


class RiskManagementAgent:
    """Risk Management Agent - 30% weight in consensus"""

    def __init__(self):
        self.weight = 0.30
        self.initialized = False

    async def initialize(self):
        """Initialize risk management capabilities"""
        try:
            self.initialized = True
            logger.info("Risk Management Agent initialized")
        except Exception as e:
            logger.error(f"Risk Management Agent initialization failed: {e}")

    async def analyze(self, message: str, orchestrator) -> Dict[str, Any]:
        """Perform risk analysis on trading requests"""
        try:
            # Extract trading intent and parameters
            risk_analysis = self._analyze_risk_factors(message, orchestrator)

            return {
                "analysis": risk_analysis["summary"],
                "confidence": risk_analysis["confidence"],
                "recommendation": risk_analysis["recommendation"],
                "reasoning": risk_analysis["reasoning"],
                "risk_metrics": risk_analysis["metrics"]
            }

        except Exception as e:
            logger.error(f"Risk Management Agent error: {e}")
            return {
                "analysis": "Risk analysis failed due to system error",
                "confidence": 0.1,
                "recommendation": "reduce_risk",
                "reasoning": f"Error: {str(e)}"
            }

    def _analyze_risk_factors(self, message: str, orchestrator) -> Dict[str, Any]:
        """Analyze risk factors for the trading request"""
        try:
            confidence = 0.6  # Base confidence for risk assessment
            recommendation = "moderate_risk"
            reasoning_parts = []
            metrics = {}

            # Check for high-risk language patterns
            high_risk_patterns = [
                r"make.*\$\d+.*today",  # "make $100 today"
                r"need.*money.*quick",   # "need money quickly"
                r"all.*in",             # "go all in"
                r"yolo",                # "YOLO trade"
                r"revenge.*trad",       # "revenge trading"
                r"double.*down",        # "double down"
            ]

            import re
            risk_flags = 0
            for pattern in high_risk_patterns:
                if re.search(pattern, message.lower()):
                    risk_flags += 1
                    reasoning_parts.append(f"High-risk language detected: {pattern}")

            # Risk assessment based on flags
            if risk_flags >= 2:
                recommendation = "high_risk"
                confidence += 0.2
                reasoning_parts.append("Multiple high-risk indicators detected")
            elif risk_flags == 1:
                recommendation = "moderate_risk"
                confidence += 0.1
                reasoning_parts.append("Some risk indicators present")
            else:
                recommendation = "low_risk"
                reasoning_parts.append("No obvious risk indicators")

            # Check for position sizing mentions
            position_size_patterns = [
                r"\$(\d+)",           # Dollar amounts
                r"(\d+)%",            # Percentage amounts
                r"(\d+)\s*shares",    # Share amounts
            ]

            position_mentions = []
            for pattern in position_size_patterns:
                matches = re.findall(pattern, message)
                position_mentions.extend(matches)

            if position_mentions:
                try:
                    # Analyze mentioned amounts
                    amounts = [float(match) for match in position_mentions if match.replace('.', '').isdigit()]
                    if amounts:
                        max_amount = max(amounts)
                        if max_amount > 10000:  # Large dollar amount
                            reasoning_parts.append(f"Large position size mentioned: ${max_amount:,.0f}")
                            if recommendation != "high_risk":
                                recommendation = "moderate_risk"
                        elif max_amount > 50:  # High percentage
                            reasoning_parts.append(f"High percentage allocation mentioned: {max_amount}%")
                            if recommendation != "high_risk":
                                recommendation = "moderate_risk"
                except:
                    pass

            # Generate risk metrics
            metrics = {
                "risk_level": recommendation,
                "risk_flags": risk_flags,
                "confidence_score": confidence,
                "position_size_warnings": len([p for p in reasoning_parts if "position size" in p.lower()])
            }

            # Generate summary based on risk level
            if recommendation == "high_risk":
                summary = "⚠️ HIGH RISK: This trading approach shows multiple risk indicators"
            elif recommendation == "moderate_risk":
                summary = "⚡ MODERATE RISK: Some risk factors identified, proceed with caution"
            else:
                summary = "✅ LOW RISK: Risk factors appear manageable"

            return {
                "summary": summary,
                "confidence": min(confidence, 0.9),
                "recommendation": recommendation,
                "reasoning": " | ".join(reasoning_parts) if reasoning_parts else "Standard risk assessment completed",
                "metrics": metrics
            }

        except Exception as e:
            logger.error(f"Risk factor analysis error: {e}")
            return {
                "summary": "Risk analysis incomplete",
                "confidence": 0.3,
                "recommendation": "moderate_risk",
                "reasoning": f"Analysis error: {str(e)}",
                "metrics": {"error": True}
            }


class SentimentAnalysisAgent:
    """Sentiment Analysis Agent - 20% weight in consensus"""

    def __init__(self):
        self.weight = 0.20
        self.initialized = False

    async def initialize(self):
        """Initialize sentiment analysis capabilities"""
        try:
            self.initialized = True
            logger.info("Sentiment Analysis Agent initialized")
        except Exception as e:
            logger.error(f"Sentiment Analysis Agent initialization failed: {e}")

    async def analyze(self, message: str, orchestrator) -> Dict[str, Any]:
        """Perform sentiment analysis on user message and market context"""
        try:
            sentiment_analysis = self._analyze_message_sentiment(message)

            return {
                "analysis": sentiment_analysis["summary"],
                "confidence": sentiment_analysis["confidence"],
                "recommendation": sentiment_analysis["recommendation"],
                "reasoning": sentiment_analysis["reasoning"],
                "sentiment_metrics": sentiment_analysis["metrics"]
            }

        except Exception as e:
            logger.error(f"Sentiment Analysis Agent error: {e}")
            return {
                "analysis": "Sentiment analysis failed due to system error",
                "confidence": 0.1,
                "recommendation": "neutral",
                "reasoning": f"Error: {str(e)}"
            }

    def _analyze_message_sentiment(self, message: str) -> Dict[str, Any]:
        """Analyze sentiment of user message"""
        try:
            confidence = 0.5
            recommendation = "neutral"
            reasoning_parts = []
            metrics = {}

            # Positive sentiment indicators
            positive_words = [
                "bullish", "buy", "long", "up", "rise", "gain", "profit",
                "strong", "good", "great", "excellent", "confident", "optimistic"
            ]

            # Negative sentiment indicators
            negative_words = [
                "bearish", "sell", "short", "down", "fall", "loss", "drop",
                "weak", "bad", "terrible", "worried", "pessimistic", "crash"
            ]

            # Emotional indicators
            emotional_words = [
                "excited", "nervous", "scared", "greedy", "desperate",
                "anxious", "frustrated", "angry", "hopeful", "fearful"
            ]

            message_lower = message.lower()

            # Count sentiment indicators
            positive_count = sum(1 for word in positive_words if word in message_lower)
            negative_count = sum(1 for word in negative_words if word in message_lower)
            emotional_count = sum(1 for word in emotional_words if word in message_lower)

            # Calculate sentiment score
            sentiment_score = positive_count - negative_count

            if sentiment_score > 1:
                recommendation = "bullish"
                reasoning_parts.append(f"Positive sentiment detected ({positive_count} positive indicators)")
                confidence += 0.2
            elif sentiment_score < -1:
                recommendation = "bearish"
                reasoning_parts.append(f"Negative sentiment detected ({negative_count} negative indicators)")
                confidence += 0.2
            else:
                recommendation = "neutral"
                reasoning_parts.append("Balanced or neutral sentiment")

            # Emotional state analysis
            if emotional_count > 0:
                reasoning_parts.append(f"Emotional language detected ({emotional_count} indicators)")
                confidence += 0.1

                # Check for specific emotional patterns
                if any(word in message_lower for word in ["desperate", "need", "must"]):
                    reasoning_parts.append("Urgency/desperation detected - caution advised")
                    recommendation = "cautious"
                    confidence += 0.2

            # Question vs statement analysis
            if "?" in message:
                reasoning_parts.append("Question format suggests information-seeking behavior")
                confidence += 0.1
            elif "!" in message:
                reasoning_parts.append("Exclamation suggests strong conviction or emotion")
                confidence += 0.1

            metrics = {
                "sentiment_score": sentiment_score,
                "positive_indicators": positive_count,
                "negative_indicators": negative_count,
                "emotional_indicators": emotional_count,
                "message_length": len(message.split()),
                "question_format": "?" in message
            }

            # Generate summary
            if recommendation == "bullish":
                summary = "📈 BULLISH SENTIMENT: User shows positive market outlook"
            elif recommendation == "bearish":
                summary = "📉 BEARISH SENTIMENT: User shows negative market outlook"
            elif recommendation == "cautious":
                summary = "⚠️ EMOTIONAL SENTIMENT: Caution advised due to emotional indicators"
            else:
                summary = "😐 NEUTRAL SENTIMENT: Balanced emotional state detected"

            return {
                "summary": summary,
                "confidence": min(confidence, 0.8),
                "recommendation": recommendation,
                "reasoning": " | ".join(reasoning_parts) if reasoning_parts else "Basic sentiment analysis completed",
                "metrics": metrics
            }

        except Exception as e:
            logger.error(f"Message sentiment analysis error: {e}")
            return {
                "summary": "Sentiment analysis incomplete",
                "confidence": 0.2,
                "recommendation": "neutral",
                "reasoning": f"Analysis error: {str(e)}",
                "metrics": {"error": True}
            }


class ExecutionAgent:
    """Execution Timing Agent - 15% weight in consensus"""

    def __init__(self):
        self.weight = 0.15
        self.initialized = False

    async def initialize(self):
        """Initialize execution timing capabilities"""
        try:
            self.initialized = True
            logger.info("Execution Agent initialized")
        except Exception as e:
            logger.error(f"Execution Agent initialization failed: {e}")

    async def analyze(self, message: str, orchestrator) -> Dict[str, Any]:
        """Analyze optimal execution timing and market conditions"""
        try:
            execution_analysis = self._analyze_execution_timing(message, orchestrator)

            return {
                "analysis": execution_analysis["summary"],
                "confidence": execution_analysis["confidence"],
                "recommendation": execution_analysis["recommendation"],
                "reasoning": execution_analysis["reasoning"],
                "execution_metrics": execution_analysis["metrics"]
            }

        except Exception as e:
            logger.error(f"Execution Agent error: {e}")
            return {
                "analysis": "Execution analysis failed due to system error",
                "confidence": 0.1,
                "recommendation": "wait",
                "reasoning": f"Error: {str(e)}"
            }

    def _analyze_execution_timing(self, message: str, orchestrator) -> Dict[str, Any]:
        """Analyze market timing and execution conditions"""
        try:
            from datetime import datetime
            import pytz

            confidence = 0.4  # Base confidence
            recommendation = "neutral"
            reasoning_parts = []
            metrics = {}

            # Get current market time
            eastern = pytz.timezone('US/Eastern')
            current_time = datetime.now(eastern)
            market_hour = current_time.hour

            # Market timing analysis
            if 9 <= market_hour <= 16:  # Market hours
                if 9 <= market_hour <= 10:  # Opening hour
                    reasoning_parts.append("Market opening hour - high volatility expected")
                    recommendation = "cautious"
                    confidence += 0.2
                elif 15 <= market_hour <= 16:  # Closing hour
                    reasoning_parts.append("Market closing hour - increased activity expected")
                    recommendation = "active"
                    confidence += 0.2
                else:  # Mid-day
                    reasoning_parts.append("Mid-day trading - normal market conditions")
                    recommendation = "normal"
                    confidence += 0.1
            else:
                reasoning_parts.append("After-hours trading - limited liquidity")
                recommendation = "wait"
                confidence += 0.3

            # Urgency analysis from message
            urgency_patterns = [
                r"now", r"immediately", r"asap", r"urgent", r"quick",
                r"today", r"right now", r"this minute"
            ]

            import re
            urgency_detected = any(re.search(pattern, message.lower()) for pattern in urgency_patterns)

            if urgency_detected:
                reasoning_parts.append("Urgency detected in request")
                if recommendation == "wait":
                    reasoning_parts.append("However, market conditions suggest waiting")
                else:
                    recommendation = "immediate"
                    confidence += 0.1

            # Volume and liquidity considerations
            day_of_week = current_time.weekday()  # 0 = Monday
            if day_of_week == 0:  # Monday
                reasoning_parts.append("Monday trading - watch for weekend news impact")
            elif day_of_week == 4:  # Friday
                reasoning_parts.append("Friday trading - potential position squaring")

            metrics = {
                "market_hour": market_hour,
                "day_of_week": day_of_week,
                "urgency_detected": urgency_detected,
                "market_session": "regular" if 9 <= market_hour <= 16 else "extended",
                "timing_score": confidence
            }

            # Generate summary
            if recommendation == "immediate":
                summary = "⚡ EXECUTE NOW: Market conditions and urgency favor immediate action"
            elif recommendation == "active":
                summary = "🎯 GOOD TIMING: Favorable execution conditions detected"
            elif recommendation == "cautious":
                summary = "⚠️ PROCEED CAREFULLY: Market timing requires caution"
            elif recommendation == "wait":
                summary = "⏰ WAIT: Market conditions not optimal for execution"
            else:
                summary = "📊 NORMAL CONDITIONS: Standard execution timing applies"

            return {
                "summary": summary,
                "confidence": min(confidence, 0.8),
                "recommendation": recommendation,
                "reasoning": " | ".join(reasoning_parts) if reasoning_parts else "Basic timing analysis completed",
                "metrics": metrics
            }

        except Exception as e:
            logger.error(f"Execution timing analysis error: {e}")
            return {
                "summary": "Execution timing analysis incomplete",
                "confidence": 0.2,
                "recommendation": "wait",
                "reasoning": f"Analysis error: {str(e)}",
                "metrics": {"error": True}
            }


# Additional methods for AtlasAIEngine
async def _generate_consensus(self, agent_responses: Dict[str, Any], message: str) -> Dict[str, Any]:
    """Generate weighted consensus from multiple agent responses"""
    try:
        if not agent_responses:
            return {
                "response": "No agent responses available for consensus",
                "confidence": 0.1,
                "reasoning": "No agents responded",
                "recommendation": "neutral"
            }

        # Agent weights (must sum to 1.0)
        agent_weights = {
            "technical": 0.35,    # Technical Analysis Agent
            "risk": 0.30,         # Risk Management Agent
            "sentiment": 0.20,    # Sentiment Analysis Agent
            "execution": 0.15     # Execution Timing Agent
        }

        # Calculate weighted confidence
        weighted_confidence = 0.0
        total_weight = 0.0

        for agent_name, response in agent_responses.items():
            if agent_name in agent_weights:
                weight = agent_weights[agent_name]
                confidence = response.get("confidence", 0.5)
                weighted_confidence += confidence * weight
                total_weight += weight

        # Normalize if not all agents responded
        if total_weight > 0:
            weighted_confidence = weighted_confidence / total_weight
        else:
            weighted_confidence = 0.5

        # Collect recommendations with weights
        recommendations = {}
        reasoning_parts = []

        for agent_name, response in agent_responses.items():
            if agent_name in agent_weights:
                weight = agent_weights[agent_name]
                recommendation = response.get("recommendation", "neutral")
                analysis = response.get("analysis", "")
                reasoning = response.get("reasoning", "")

                # Weight the recommendation
                if recommendation not in recommendations:
                    recommendations[recommendation] = 0.0
                recommendations[recommendation] += weight

                # Add to reasoning with weight indication
                reasoning_parts.append(f"**{agent_name.title()} ({weight*100:.0f}%)**: {analysis}")
                if reasoning:
                    reasoning_parts.append(f"  └─ Reasoning: {reasoning}")

        # Determine consensus recommendation
        if recommendations:
            consensus_recommendation = max(recommendations.items(), key=lambda x: x[1])[0]
            recommendation_confidence = recommendations[consensus_recommendation]
        else:
            consensus_recommendation = "neutral"
            recommendation_confidence = 0.5

        # Check for disagreement
        disagreement_penalty = 0.0
        if len(recommendations) > 1:
            # Calculate disagreement penalty based on how split the recommendations are
            sorted_recs = sorted(recommendations.values(), reverse=True)
            if len(sorted_recs) >= 2:
                disagreement_ratio = sorted_recs[1] / sorted_recs[0] if sorted_recs[0] > 0 else 0
                disagreement_penalty = disagreement_ratio * 0.2  # Up to 20% penalty
                reasoning_parts.append(f"⚠️ **Agent Disagreement**: {disagreement_ratio:.1%} disagreement detected")

        # Apply disagreement penalty
        final_confidence = max(0.1, weighted_confidence - disagreement_penalty)

        # Generate consensus summary
        consensus_summary = f"🤖 **Multi-Agent Consensus**: {consensus_recommendation.upper()}"
        if recommendation_confidence >= 0.7:
            consensus_summary += " (Strong Agreement)"
        elif recommendation_confidence >= 0.5:
            consensus_summary += " (Moderate Agreement)"
        else:
            consensus_summary += " (Weak Agreement)"

        # Add confidence indicator
        if final_confidence >= 0.8:
            confidence_indicator = "🟢 High Confidence"
        elif final_confidence >= 0.6:
            confidence_indicator = "🟡 Moderate Confidence"
        else:
            confidence_indicator = "🔴 Low Confidence"

        consensus_response = f"{consensus_summary}\n{confidence_indicator} ({final_confidence:.1%})\n\n" + "\n\n".join(reasoning_parts)

        return {
            "response": consensus_response,
            "confidence": final_confidence,
            "reasoning": f"Weighted consensus from {len(agent_responses)} agents",
            "recommendation": consensus_recommendation,
            "agent_weights": agent_weights,
            "disagreement_penalty": disagreement_penalty,
            "recommendation_distribution": recommendations
        }

    except Exception as e:
        logger.error(f"Weighted consensus generation failed: {e}")
        return {
            "response": f"Consensus generation failed: {str(e)}",
            "confidence": 0.1,
            "reasoning": "Error in consensus calculation",
            "recommendation": "neutral"
        }

    async def _generate_chain_of_thought(self, agent_responses: Dict[str, Any], consensus: Dict[str, Any], message: str) -> Dict[str, Any]:
        """Generate chain-of-thought reasoning with educational explanations"""
        try:
            # Extract key information
            symbol = _extract_symbol_from_message(message)
            user_intent = _analyze_user_intent(message)

            # Step-by-step reasoning process
            reasoning_steps = []

            # Step 1: Understanding the Request
            reasoning_steps.append({
                "step": 1,
                "title": "🎯 Understanding Your Request",
                "content": f"You asked: '{message}'\n" +
                          f"I detected you're interested in: {symbol if symbol else 'general trading advice'}\n" +
                          f"Intent: {user_intent['description']}\n" +
                          "Let me break down my analysis step by step...",
                "analogy": "Think of this like a doctor's examination - first I need to understand exactly what you're asking before I can provide the right advice."
            })

        # Step 2: Multi-Agent Analysis
        agent_summary = []
        for agent_name, response in agent_responses.items():
            recommendation = response.get("recommendation", "neutral")
            confidence = response.get("confidence", 0.5)
            reasoning = response.get("reasoning", "")

            agent_summary.append(f"**{agent_name.title()} Agent**: {recommendation.upper()} ({confidence:.0%} confidence)")
            if reasoning:
                agent_summary.append(f"  └─ {reasoning}")

        reasoning_steps.append({
            "step": 2,
            "title": "🤖 Multi-Agent Analysis",
            "content": "I consulted my team of 4 specialized AI agents:\n\n" + "\n".join(agent_summary),
            "analogy": "Like getting a second opinion from multiple specialists - each agent looks at different aspects of your request to give you the most complete picture."
        })

        # Step 3: Risk Assessment
        risk_agent_response = agent_responses.get("risk", {})
        risk_level = risk_agent_response.get("recommendation", "moderate_risk")
        risk_reasoning = risk_agent_response.get("reasoning", "Standard risk assessment")

        risk_explanation = {
            "high_risk": "⚠️ HIGH RISK: This approach shows multiple warning signs. Like driving in a storm - possible but requires extreme caution.",
            "moderate_risk": "⚡ MODERATE RISK: Some caution needed. Like crossing a busy street - look both ways and proceed carefully.",
            "low_risk": "✅ LOW RISK: Relatively safe approach. Like walking on a sidewalk - still pay attention but generally safe."
        }

        reasoning_steps.append({
            "step": 3,
            "title": "🛡️ Risk Assessment",
            "content": f"{risk_explanation.get(risk_level, 'Risk assessment completed')}\n\nDetails: {risk_reasoning}",
            "analogy": "Risk management is like wearing a seatbelt - it doesn't prevent accidents, but it protects you when things go wrong."
        })

        # Step 4: Technical Analysis (if available)
        tech_agent_response = agent_responses.get("technical", {})
        if tech_agent_response:
            tech_recommendation = tech_agent_response.get("recommendation", "neutral")
            tech_details = tech_agent_response.get("technical_details", {})

            tech_content = f"Technical outlook: **{tech_recommendation.upper()}**\n"

            if "ttm_squeeze" in tech_details:
                ttm = tech_details["ttm_squeeze"]
                if ttm.get("active"):
                    tech_content += "🔄 TTM Squeeze is ACTIVE - like a coiled spring ready to release energy\n"
                else:
                    momentum = ttm.get("momentum", "neutral")
                    tech_content += f"🚀 TTM Squeeze fired {momentum.upper()} - the spring has released!\n"

            reasoning_steps.append({
                "step": 4,
                "title": "📊 Technical Analysis",
                "content": tech_content,
                "analogy": "Technical analysis is like reading the market's body language - it tells us what the market is feeling and where it might go next."
            })

        # Step 5: Final Recommendation
        final_recommendation = consensus.get("recommendation", "neutral")
        final_confidence = consensus.get("confidence", 0.5)

        recommendation_explanations = {
            "buy": "📈 BUY RECOMMENDATION: The analysis suggests this could be a good opportunity to enter a long position.",
            "sell": "📉 SELL RECOMMENDATION: The analysis suggests this could be a good opportunity to enter a short position or exit longs.",
            "neutral": "😐 NEUTRAL RECOMMENDATION: The analysis suggests waiting for clearer signals before taking action.",
            "wait": "⏰ WAIT RECOMMENDATION: Current conditions suggest patience is the best strategy.",
            "cautious": "⚠️ PROCEED WITH CAUTION: Some opportunity exists but with elevated risk."
        }

        reasoning_steps.append({
            "step": 5,
            "title": "🎯 Final Recommendation",
            "content": f"{recommendation_explanations.get(final_recommendation, 'Analysis complete')}\n\n" +
                      f"**Confidence Level**: {final_confidence:.0%}\n" +
                      f"**Reasoning**: {consensus.get('reasoning', 'Based on multi-agent consensus')}",
            "analogy": "Like a GPS giving you the final route - I've considered all the traffic (market conditions), road quality (technical analysis), and your driving style (risk tolerance) to give you the best path forward."
        })

        # Generate educational summary
        educational_notes = []

        if symbol:
            educational_notes.append(f"📚 **Learning Point**: When analyzing {symbol}, always consider multiple timeframes and confirm signals with volume.")

        if "make" in message.lower() and any(char.isdigit() for char in message):
            educational_notes.append("💡 **Reality Check**: Setting specific profit targets is good, but remember that markets are unpredictable. Focus on process over outcomes.")

        if any(word in message.lower() for word in ["quick", "fast", "today", "now"]):
            educational_notes.append("⏰ **Patience Lesson**: The best traders are patient. Quick profits often lead to quick losses. Quality setups take time to develop.")

        educational_notes.append("🎓 **Key Takeaway**: Successful trading is about managing risk, not predicting the future. Always have a plan before you enter any trade.")

        # Compile final response
        response_parts = []

        # Add step-by-step reasoning
        for step in reasoning_steps:
            response_parts.append(f"## Step {step['step']}: {step['title']}\n")
            response_parts.append(f"{step['content']}\n")
            response_parts.append(f"*{step['analogy']}*\n")

        # Add educational notes
        if educational_notes:
            response_parts.append("## 📚 Educational Notes\n")
            response_parts.append("\n".join(educational_notes))

        # Add next steps
        response_parts.append("\n## 🚀 What's Next?\n")
        if final_recommendation in ["buy", "sell"]:
            response_parts.append("• Review your position sizing (never risk more than 2% of your account)\n")
            response_parts.append("• Set your stop loss BEFORE entering the trade\n")
            response_parts.append("• Have a profit target in mind\n")
            response_parts.append("• Monitor the trade but don't micromanage")
        else:
            response_parts.append("• Continue monitoring for clearer signals\n")
            response_parts.append("• Use this time to study and prepare\n")
            response_parts.append("• Review your trading plan and risk management rules")

        final_response = "\n".join(response_parts)

        return {
            "response": final_response,
            "confidence": final_confidence,
            "reasoning_steps": reasoning_steps,
            "educational_notes": educational_notes
        }

    except Exception as e:
        logger.error(f"Chain-of-thought generation failed: {e}")
        return {
            "response": f"I've analyzed your request and here's my assessment:\n\n{consensus.get('response', 'Analysis in progress...')}\n\n*Note: Detailed reasoning temporarily unavailable*",
            "confidence": consensus.get("confidence", 0.5),
            "reasoning_steps": [],
            "educational_notes": ["Chain-of-thought analysis encountered an error but basic analysis is available."]
        }

def _extract_symbol_from_message(message: str) -> Optional[str]:
    """Extract stock symbol from message"""
    import re
    patterns = [
        r'\b([A-Z]{1,5})\b',  # 1-5 uppercase letters
        r'\$([A-Z]{1,5})\b',  # $SYMBOL format
    ]

    for pattern in patterns:
        matches = re.findall(pattern, message.upper())
        if matches:
            for match in matches:
                if len(match) >= 1 and len(match) <= 5:
                    return match
    return None

def _analyze_user_intent(message: str) -> Dict[str, str]:
    """Analyze user intent from message"""
    message_lower = message.lower()

    if any(word in message_lower for word in ["analyze", "analysis", "look at", "thoughts on"]):
        return {"type": "analysis", "description": "seeking market analysis"}
    elif any(word in message_lower for word in ["buy", "sell", "trade", "position"]):
        return {"type": "trading", "description": "considering a trade"}
    elif any(word in message_lower for word in ["make", "profit", "money", "earn"]):
        return {"type": "profit_seeking", "description": "seeking profit opportunities"}
    elif any(word in message_lower for word in ["learn", "explain", "how", "what", "why"]):
        return {"type": "educational", "description": "seeking education/explanation"}
    else:
        return {"type": "general", "description": "general trading inquiry"}


# Advanced Conversational Intelligence Methods
class ConversationalIntelligence:
    """Advanced conversational features for A.T.L.A.S"""

    @staticmethod
    async def analyze_emotional_state(message: str) -> Dict[str, Any]:
        """Analyze emotional state from user message"""
        try:
            message_lower = message.lower()

            # Emotional indicators
            emotions = {
                "excitement": ["excited", "pumped", "thrilled", "amazing", "awesome"],
                "anxiety": ["worried", "nervous", "scared", "anxious", "concerned"],
                "greed": ["rich", "fortune", "millions", "big money", "huge profits"],
                "fear": ["afraid", "terrified", "panic", "crash", "lose everything"],
                "desperation": ["need", "must", "have to", "desperate", "urgent"],
                "confidence": ["sure", "certain", "confident", "know", "guaranteed"],
                "frustration": ["frustrated", "angry", "mad", "annoyed", "fed up"]
            }

            detected_emotions = []
            emotion_scores = {}

            for emotion, keywords in emotions.items():
                score = sum(1 for keyword in keywords if keyword in message_lower)
                if score > 0:
                    detected_emotions.append(emotion)
                    emotion_scores[emotion] = score

            # Determine primary emotion
            primary_emotion = "neutral"
            if emotion_scores:
                primary_emotion = max(emotion_scores.items(), key=lambda x: x[1])[0]

            # Risk level based on emotions
            high_risk_emotions = ["greed", "desperation", "fear", "excitement"]
            risk_level = "high" if primary_emotion in high_risk_emotions else "moderate" if detected_emotions else "low"

            return {
                "primary_emotion": primary_emotion,
                "detected_emotions": detected_emotions,
                "emotion_scores": emotion_scores,
                "risk_level": risk_level,
                "needs_coaching": primary_emotion in ["desperation", "greed", "fear"]
            }

        except Exception as e:
            logger.error(f"Emotional analysis error: {e}")
            return {"primary_emotion": "neutral", "risk_level": "moderate", "needs_coaching": False}

    @staticmethod
    async def parse_trading_goals(message: str) -> Dict[str, Any]:
        """Parse trading goals from user message"""
        try:
            import re

            goals = {
                "profit_target": None,
                "timeframe": None,
                "risk_tolerance": None,
                "specific_amount": None,
                "percentage_target": None
            }

            # Extract dollar amounts
            dollar_matches = re.findall(r'\$(\d+(?:,\d{3})*(?:\.\d{2})?)', message)
            if dollar_matches:
                # Convert to float, removing commas
                amounts = [float(match.replace(',', '')) for match in dollar_matches]
                goals["specific_amount"] = max(amounts)  # Take the largest amount mentioned

            # Extract percentages
            percent_matches = re.findall(r'(\d+(?:\.\d+)?)%', message)
            if percent_matches:
                percentages = [float(match) for match in percent_matches]
                goals["percentage_target"] = max(percentages)

            # Extract timeframes
            timeframe_patterns = {
                "today": r'\b(today|this day)\b',
                "week": r'\b(this week|weekly|week)\b',
                "month": r'\b(this month|monthly|month)\b',
                "year": r'\b(this year|yearly|year|annual)\b'
            }

            for timeframe, pattern in timeframe_patterns.items():
                if re.search(pattern, message.lower()):
                    goals["timeframe"] = timeframe
                    break

            # Determine if goals are realistic
            is_realistic = True
            unrealistic_reasons = []

            if goals["specific_amount"]:
                if goals["timeframe"] == "today" and goals["specific_amount"] > 1000:
                    is_realistic = False
                    unrealistic_reasons.append("Daily profit target too high")
                elif goals["timeframe"] == "week" and goals["specific_amount"] > 5000:
                    is_realistic = False
                    unrealistic_reasons.append("Weekly profit target too high")

            if goals["percentage_target"]:
                if goals["timeframe"] == "today" and goals["percentage_target"] > 10:
                    is_realistic = False
                    unrealistic_reasons.append("Daily percentage target too high")

            goals["is_realistic"] = is_realistic
            goals["unrealistic_reasons"] = unrealistic_reasons

            return goals

        except Exception as e:
            logger.error(f"Goal parsing error: {e}")
            return {"is_realistic": True, "unrealistic_reasons": []}

    @staticmethod
    async def perform_reality_check(message: str, goals: Dict[str, Any]) -> Dict[str, Any]:
        """Perform reality check on user expectations"""
        try:
            reality_check = {
                "needs_reality_check": False,
                "severity": "low",
                "suggestions": [],
                "educational_response": ""
            }

            # Check for unrealistic goals
            if not goals.get("is_realistic", True):
                reality_check["needs_reality_check"] = True
                reality_check["severity"] = "high"

                if "Daily profit target too high" in goals.get("unrealistic_reasons", []):
                    reality_check["suggestions"].append("Consider smaller, more achievable daily targets (1-3%)")
                    reality_check["educational_response"] = """
🎯 **Reality Check**: Making large profits in a single day is like trying to hit a home run every at-bat in baseball.

**More Realistic Approach**:
• Aim for 1-3% daily gains consistently
• Focus on process over outcomes
• Remember: even professional traders have losing days
• Small consistent gains compound over time

**Analogy**: Think of trading like farming - you plant seeds (trades), tend them carefully (risk management), and harvest when ready. You can't force a crop to grow faster than nature allows."""

                if "Weekly profit target too high" in goals.get("unrealistic_reasons", []):
                    reality_check["suggestions"].append("Weekly targets of 5-15% are more sustainable")

            # Check for emotional language requiring coaching
            message_lower = message.lower()
            emotional_triggers = [
                ("need money", "Financial pressure can lead to poor trading decisions"),
                ("lost money", "Revenge trading often leads to bigger losses"),
                ("get rich", "Trading is not a get-rich-quick scheme"),
                ("guaranteed", "No trading strategy is guaranteed to work"),
                ("sure thing", "There are no sure things in trading")
            ]

            for trigger, warning in emotional_triggers:
                if trigger in message_lower:
                    reality_check["needs_reality_check"] = True
                    reality_check["severity"] = "moderate"
                    reality_check["suggestions"].append(warning)

            return reality_check

        except Exception as e:
            logger.error(f"Reality check error: {e}")
            return {"needs_reality_check": False, "severity": "low", "suggestions": []}

async def _generate_educational_response(self, message: str) -> AIResponse:
    """Generate educational response using AI"""
    try:
        client = await self._ensure_openai_client()

        response = await client.chat.completions.create(
            model=self.config["model"],
            messages=[
                {
                    "role": "system",
                    "content": """You are an expert trading educator. Provide clear, educational
                    responses about trading concepts, market analysis, and financial strategies.
                    Use examples and analogies to make complex concepts understandable."""
                },
                {"role": "user", "content": message}
            ],
            temperature=0.3,
            max_tokens=600
        )

        return AIResponse(
            response=response.choices[0].message.content,
            type="education",
            confidence=0.8
        )

    except Exception as e:
        logger.error(f"Educational response generation failed: {e}")
        return await self._fallback_response(message, "education")

async def _generate_portfolio_response(self, message: str, portfolio_data: Any) -> AIResponse:
    """Generate portfolio response with context"""
    try:
        client = await self._ensure_openai_client()

        portfolio_context = ""
        if portfolio_data:
            portfolio_context = f"Current portfolio data: {portfolio_data}"
        else:
            portfolio_context = "Portfolio data is currently unavailable."

        response = await client.chat.completions.create(
            model=self.config["model"],
            messages=[
                {
                    "role": "system",
                    "content": f"""You are a portfolio management assistant. Help the user understand
                    their portfolio and provide insights. {portfolio_context}"""
                },
                {"role": "user", "content": message}
            ],
            temperature=0.3,
            max_tokens=500
        )

        return AIResponse(
            response=response.choices[0].message.content,
            type="portfolio",
            confidence=0.8,
            context={"portfolio_data": portfolio_data}
        )

    except Exception as e:
        logger.error(f"Portfolio response generation failed: {e}")
        return await self._fallback_response(message, "portfolio")

async def _generate_market_response(self, message: str, symbol: str, market_data: Any) -> AIResponse:
    """Generate market response with context"""
    try:
        client = await self._ensure_openai_client()

        market_context = ""
        if market_data and symbol:
            market_context = f"Current data for {symbol}: {market_data}"
        elif symbol:
            market_context = f"Symbol requested: {symbol} (data unavailable)"
        else:
            market_context = "No specific symbol identified in request."

        response = await client.chat.completions.create(
            model=self.config["model"],
            messages=[
                {
                    "role": "system",
                    "content": f"""You are a market data assistant. Provide market insights and
                    analysis based on available data. {market_context}"""
                },
                {"role": "user", "content": message}
            ],
            temperature=0.3,
            max_tokens=500
        )

        return AIResponse(
            response=response.choices[0].message.content,
            type="market_data",
            confidence=0.8,
            context={"symbol": symbol, "market_data": market_data}
        )

    except Exception as e:
        logger.error(f"Market response generation failed: {e}")
        return await self._fallback_response(message, "market_data")

# Add these methods to AtlasAIEngine class
AtlasAIEngine._generate_consensus = _generate_consensus
AtlasAIEngine._generate_educational_response = _generate_educational_response
AtlasAIEngine._generate_portfolio_response = _generate_portfolio_response
AtlasAIEngine._generate_market_response = _generate_market_response
