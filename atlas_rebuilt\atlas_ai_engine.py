"""
A.T.L.A.S AI Engine - Multi-Agent System with Background Loading
Conversational AI with chain-of-thought analysis and lazy ML model loading
"""

import asyncio
import logging
import json
from datetime import datetime
from typing import Optional, List, Dict, Any, Union

from config import get_api_config
from models import AIResponse, EngineStatus

logger = logging.getLogger(__name__)


class AtlasAIEngine:
    """
    Advanced AI engine with multi-agent system and lazy loading
    """
    
    def __init__(self):
        self.config = get_api_config("openai")
        self.status = EngineStatus.INITIALIZING
        
        # OpenAI client (lazy loaded)
        self._openai_client = None
        self._client_lock = asyncio.Lock()
        
        # Multi-agent system
        self.agents = {
            "technical": TechnicalAnalysisAgent(),
            "risk": RiskManagementAgent(),
            "sentiment": SentimentAnalysisAgent(),
            "execution": ExecutionAgent()
        }
        
        # Conversation context
        self.conversation_memory = {}
        self.user_profiles = {}
        
        logger.info("🧠 AI Engine created - OpenAI client will load on demand")
    
    async def initialize(self):
        """Initialize AI engine with lazy loading"""
        try:
            # Test OpenAI connection
            await self._ensure_openai_client()
            
            # Initialize agents
            for name, agent in self.agents.items():
                await agent.initialize()
                logger.info(f"✅ {name.title()} agent initialized")
            
            self.status = EngineStatus.ACTIVE
            logger.info("✅ AI Engine initialization completed")
            
        except Exception as e:
            logger.error(f"❌ AI Engine initialization failed: {e}")
            self.status = EngineStatus.FAILED
            # Continue with degraded functionality
    
    async def _ensure_openai_client(self):
        """Ensure OpenAI client is initialized"""
        if self._openai_client is None:
            async with self._client_lock:
                if self._openai_client is None:
                    try:
                        import openai
                        self._openai_client = openai.AsyncOpenAI(
                            api_key=self.config["api_key"]
                        )
                        
                        # Test connection
                        await self._openai_client.models.list()
                        logger.info("✅ OpenAI client initialized and tested")
                        
                    except Exception as e:
                        logger.error(f"❌ OpenAI client initialization failed: {e}")
                        raise
        
        return self._openai_client
    
    async def process_message(self, message: str, session_id: Optional[str], orchestrator) -> AIResponse:
        """Process user message with multi-agent analysis"""
        try:
            # Ensure OpenAI client is available
            client = await self._ensure_openai_client()
            
            # Analyze message intent
            intent = await self._analyze_intent(message)
            
            # Route to appropriate processing
            if intent["type"] == "trading_analysis":
                return await self._process_trading_analysis(message, session_id, orchestrator)
            elif intent["type"] == "education":
                return await self._process_education_query(message, session_id, orchestrator)
            elif intent["type"] == "portfolio":
                return await self._process_portfolio_query(message, session_id, orchestrator)
            elif intent["type"] == "market_data":
                return await self._process_market_query(message, session_id, orchestrator)
            else:
                return await self._process_general_chat(message, session_id, orchestrator)
                
        except Exception as e:
            logger.error(f"Error processing message: {e}")
            return AIResponse(
                response="I encountered an error processing your message. Please try again.",
                type="error",
                confidence=0.0,
                context={"error": str(e)}
            )
    
    async def _analyze_intent(self, message: str) -> Dict[str, Any]:
        """Analyze user message intent"""
        try:
            client = await self._ensure_openai_client()
            
            response = await client.chat.completions.create(
                model=self.config["model"],
                messages=[
                    {
                        "role": "system",
                        "content": """You are an intent classifier for a trading AI system. 
                        Classify the user's message into one of these categories:
                        - trading_analysis: Questions about stocks, technical analysis, trading signals
                        - education: Questions about trading concepts, learning requests
                        - portfolio: Questions about positions, P&L, account status
                        - market_data: Requests for quotes, market information
                        - general_chat: Greetings, general conversation
                        
                        Respond with JSON: {"type": "category", "confidence": 0.0-1.0, "entities": []}"""
                    },
                    {"role": "user", "content": message}
                ],
                temperature=0.1,
                max_tokens=200
            )
            
            result = json.loads(response.choices[0].message.content)
            return result
            
        except Exception as e:
            logger.error(f"Intent analysis failed: {e}")
            return {"type": "general_chat", "confidence": 0.5, "entities": []}
    
    async def _process_trading_analysis(self, message: str, session_id: str, orchestrator) -> AIResponse:
        """Process trading analysis request with multi-agent system"""
        try:
            # Get multi-agent consensus
            agent_responses = {}
            
            # Technical analysis
            if "technical" in self.agents:
                agent_responses["technical"] = await self.agents["technical"].analyze(message, orchestrator)
            
            # Risk assessment
            if "risk" in self.agents:
                agent_responses["risk"] = await self.agents["risk"].analyze(message, orchestrator)
            
            # Sentiment analysis
            if "sentiment" in self.agents:
                agent_responses["sentiment"] = await self.agents["sentiment"].analyze(message, orchestrator)
            
            # Generate consensus response
            consensus = await self._generate_consensus(agent_responses, message)
            
            return AIResponse(
                response=consensus["response"],
                type="trading_analysis",
                confidence=consensus["confidence"],
                context={
                    "agent_responses": agent_responses,
                    "consensus_reasoning": consensus["reasoning"]
                }
            )
            
        except Exception as e:
            logger.error(f"Trading analysis failed: {e}")
            return await self._fallback_response(message, "trading_analysis")
    
    async def _process_education_query(self, message: str, session_id: str, orchestrator) -> AIResponse:
        """Process educational query"""
        try:
            # Use education engine if available
            if hasattr(orchestrator, '_education_engine') and orchestrator._education_engine:
                education_response = await orchestrator._education_engine.process_query({
                    "question": message,
                    "difficulty_level": "beginner"
                })
                return education_response
            
            # Fallback to AI response
            return await self._generate_educational_response(message)
            
        except Exception as e:
            logger.error(f"Education query failed: {e}")
            return await self._fallback_response(message, "education")
    
    async def _process_portfolio_query(self, message: str, session_id: str, orchestrator) -> AIResponse:
        """Process portfolio-related query"""
        try:
            # Get portfolio data if trading engine is available
            portfolio_data = None
            if hasattr(orchestrator, '_trading_engine') and orchestrator._trading_engine:
                portfolio_data = await orchestrator._trading_engine.get_portfolio_summary()
            
            # Generate response with portfolio context
            return await self._generate_portfolio_response(message, portfolio_data)
            
        except Exception as e:
            logger.error(f"Portfolio query failed: {e}")
            return await self._fallback_response(message, "portfolio")
    
    async def _process_market_query(self, message: str, session_id: str, orchestrator) -> AIResponse:
        """Process market data query"""
        try:
            # Extract symbol from message
            symbol = self._extract_symbol(message)
            
            # Get market data if market engine is available
            market_data = None
            if symbol and hasattr(orchestrator, '_market_engine') and orchestrator._market_engine:
                market_data = await orchestrator._market_engine.get_quote(symbol)
            
            # Generate response with market context
            return await self._generate_market_response(message, symbol, market_data)
            
        except Exception as e:
            logger.error(f"Market query failed: {e}")
            return await self._fallback_response(message, "market_data")
    
    async def _process_general_chat(self, message: str, session_id: str, orchestrator) -> AIResponse:
        """Process general conversation"""
        try:
            client = await self._ensure_openai_client()
            
            # Get conversation context
            context = self.conversation_memory.get(session_id, [])
            
            # Build conversation messages
            messages = [
                {
                    "role": "system",
                    "content": """You are A.T.L.A.S, an advanced AI trading assistant. You are knowledgeable, 
                    helpful, and mentor-like in your communication style. You help users with trading, 
                    market analysis, and financial education. Keep responses conversational and engaging."""
                }
            ]
            
            # Add recent context
            for ctx in context[-5:]:  # Last 5 messages
                messages.append({"role": "user", "content": ctx["message"]})
                messages.append({"role": "assistant", "content": ctx["response"]})
            
            # Add current message
            messages.append({"role": "user", "content": message})
            
            response = await client.chat.completions.create(
                model=self.config["model"],
                messages=messages,
                temperature=self.config["temperature"],
                max_tokens=500
            )
            
            ai_response = response.choices[0].message.content
            
            # Update conversation memory
            if session_id not in self.conversation_memory:
                self.conversation_memory[session_id] = []
            
            self.conversation_memory[session_id].append({
                "message": message,
                "response": ai_response,
                "timestamp": datetime.now()
            })
            
            # Keep only recent messages
            if len(self.conversation_memory[session_id]) > 20:
                self.conversation_memory[session_id] = self.conversation_memory[session_id][-20:]
            
            return AIResponse(
                response=ai_response,
                type="chat",
                confidence=0.8
            )
            
        except Exception as e:
            logger.error(f"General chat failed: {e}")
            return await self._fallback_response(message, "chat")
    
    async def _fallback_response(self, message: str, response_type: str) -> AIResponse:
        """Generate fallback response when AI is unavailable"""
        fallback_responses = {
            "trading_analysis": "I'm currently unable to perform detailed trading analysis. Please try again in a moment.",
            "education": "I'm currently unable to access educational content. Please try again in a moment.",
            "portfolio": "I'm currently unable to access portfolio data. Please try again in a moment.",
            "market_data": "I'm currently unable to fetch market data. Please try again in a moment.",
            "chat": "Hello! I'm A.T.L.A.S, your AI trading assistant. I'm currently initializing my systems. Please try again in a moment."
        }
        
        return AIResponse(
            response=fallback_responses.get(response_type, "I'm currently initializing. Please try again in a moment."),
            type="fallback",
            confidence=0.3
        )
    
    def _extract_symbol(self, message: str) -> Optional[str]:
        """Extract stock symbol from message"""
        import re
        # Simple regex to find stock symbols (1-5 uppercase letters)
        match = re.search(r'\b[A-Z]{1,5}\b', message.upper())
        return match.group() if match else None
    
    async def cleanup(self):
        """Cleanup AI engine resources"""
        try:
            # Cleanup agents
            for agent in self.agents.values():
                if hasattr(agent, 'cleanup'):
                    await agent.cleanup()
            
            # Clear memory
            self.conversation_memory.clear()
            
            logger.info("✅ AI Engine cleanup completed")
            
        except Exception as e:
            logger.error(f"Error during AI engine cleanup: {e}")


# Agent Classes (simplified for now)
class TechnicalAnalysisAgent:
    async def initialize(self): pass
    async def analyze(self, message: str, orchestrator): 
        return {"analysis": "Technical analysis placeholder", "confidence": 0.7}

class RiskManagementAgent:
    async def initialize(self): pass
    async def analyze(self, message: str, orchestrator): 
        return {"analysis": "Risk analysis placeholder", "confidence": 0.7}

class SentimentAnalysisAgent:
    async def initialize(self): pass
    async def analyze(self, message: str, orchestrator): 
        return {"analysis": "Sentiment analysis placeholder", "confidence": 0.7}

class ExecutionAgent:
    async def initialize(self): pass
    async def analyze(self, message: str, orchestrator):
        return {"analysis": "Execution analysis placeholder", "confidence": 0.7}


# Additional methods for AtlasAIEngine
async def _generate_consensus(self, agent_responses: Dict[str, Any], message: str) -> Dict[str, Any]:
    """Generate consensus from multiple agent responses"""
    try:
        # Simple consensus logic - can be enhanced
        total_confidence = sum(resp.get("confidence", 0.5) for resp in agent_responses.values())
        avg_confidence = total_confidence / len(agent_responses) if agent_responses else 0.5

        # Combine analyses
        combined_analysis = []
        for agent_name, response in agent_responses.items():
            analysis = response.get("analysis", "")
            if analysis:
                combined_analysis.append(f"{agent_name.title()}: {analysis}")

        consensus_response = "\n".join(combined_analysis) if combined_analysis else "Analysis in progress..."

        return {
            "response": consensus_response,
            "confidence": avg_confidence,
            "reasoning": f"Consensus from {len(agent_responses)} agents"
        }

    except Exception as e:
        logger.error(f"Consensus generation failed: {e}")
        return {
            "response": "I'm analyzing your request. Please try again in a moment.",
            "confidence": 0.3,
            "reasoning": "Consensus generation failed"
        }

async def _generate_educational_response(self, message: str) -> AIResponse:
    """Generate educational response using AI"""
    try:
        client = await self._ensure_openai_client()

        response = await client.chat.completions.create(
            model=self.config["model"],
            messages=[
                {
                    "role": "system",
                    "content": """You are an expert trading educator. Provide clear, educational
                    responses about trading concepts, market analysis, and financial strategies.
                    Use examples and analogies to make complex concepts understandable."""
                },
                {"role": "user", "content": message}
            ],
            temperature=0.3,
            max_tokens=600
        )

        return AIResponse(
            response=response.choices[0].message.content,
            type="education",
            confidence=0.8
        )

    except Exception as e:
        logger.error(f"Educational response generation failed: {e}")
        return await self._fallback_response(message, "education")

async def _generate_portfolio_response(self, message: str, portfolio_data: Any) -> AIResponse:
    """Generate portfolio response with context"""
    try:
        client = await self._ensure_openai_client()

        portfolio_context = ""
        if portfolio_data:
            portfolio_context = f"Current portfolio data: {portfolio_data}"
        else:
            portfolio_context = "Portfolio data is currently unavailable."

        response = await client.chat.completions.create(
            model=self.config["model"],
            messages=[
                {
                    "role": "system",
                    "content": f"""You are a portfolio management assistant. Help the user understand
                    their portfolio and provide insights. {portfolio_context}"""
                },
                {"role": "user", "content": message}
            ],
            temperature=0.3,
            max_tokens=500
        )

        return AIResponse(
            response=response.choices[0].message.content,
            type="portfolio",
            confidence=0.8,
            context={"portfolio_data": portfolio_data}
        )

    except Exception as e:
        logger.error(f"Portfolio response generation failed: {e}")
        return await self._fallback_response(message, "portfolio")

async def _generate_market_response(self, message: str, symbol: str, market_data: Any) -> AIResponse:
    """Generate market response with context"""
    try:
        client = await self._ensure_openai_client()

        market_context = ""
        if market_data and symbol:
            market_context = f"Current data for {symbol}: {market_data}"
        elif symbol:
            market_context = f"Symbol requested: {symbol} (data unavailable)"
        else:
            market_context = "No specific symbol identified in request."

        response = await client.chat.completions.create(
            model=self.config["model"],
            messages=[
                {
                    "role": "system",
                    "content": f"""You are a market data assistant. Provide market insights and
                    analysis based on available data. {market_context}"""
                },
                {"role": "user", "content": message}
            ],
            temperature=0.3,
            max_tokens=500
        )

        return AIResponse(
            response=response.choices[0].message.content,
            type="market_data",
            confidence=0.8,
            context={"symbol": symbol, "market_data": market_data}
        )

    except Exception as e:
        logger.error(f"Market response generation failed: {e}")
        return await self._fallback_response(message, "market_data")

# Add these methods to AtlasAIEngine class
AtlasAIEngine._generate_consensus = _generate_consensus
AtlasAIEngine._generate_educational_response = _generate_educational_response
AtlasAIEngine._generate_portfolio_response = _generate_portfolio_response
AtlasAIEngine._generate_market_response = _generate_market_response
