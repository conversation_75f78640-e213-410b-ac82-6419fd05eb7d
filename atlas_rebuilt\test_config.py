#!/usr/bin/env python3
"""
Test configuration system with validation mode
"""

import os
import sys

# Set validation mode before importing config
os.environ['VALIDATION_MODE'] = 'true'

try:
    from config import settings, validate_environment, get_api_config

    print("🔧 Testing A.T.L.A.S Configuration System")
    print("=" * 50)

    print(f"Validation Mode: {settings.VALIDATION_MODE}")
    print(f"Environment: {settings.ENVIRONMENT}")

    # Test environment validation
    env_status = validate_environment()
    print(f"\nEnvironment Status: {'✅ Valid' if env_status['valid'] else '❌ Invalid'}")
    print(f"Validation Mode: {env_status.get('validation_mode', False)}")
    print(f"Errors: {len(env_status['errors'])}")
    print(f"Warnings: {len(env_status['warnings'])}")

    if env_status['errors']:
        print("  Error details:", env_status['errors'])
    if env_status['warnings']:
        print("  Warning details:", env_status['warnings'][:3])  # Show first 3

    # Test API configurations
    print(f"\nAPI Configurations:")
    apis = ["alpaca", "fmp", "openai", "predicto"]
    for api in apis:
        config = get_api_config(api)
        available = config.get('available', False)
        print(f"  {api}: {'✅ Available' if available else '❌ Not available'}")

    print(f"\nAvailable APIs: {settings.get_available_apis()}")

    print("\n✅ Configuration test completed successfully!")

except Exception as e:
    print(f"❌ Configuration test failed: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
