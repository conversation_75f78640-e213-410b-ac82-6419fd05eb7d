#!/usr/bin/env python3
"""Test configuration loading"""

import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

print("Environment variables loaded:")
for key, value in os.environ.items():
    if any(prefix in key for prefix in ["APCA_", "FMP_", "OPENAI_", "PREDICTO_"]):
        print(f"{key} = {value[:20]}..." if len(value) > 20 else f"{key} = {value}")

print("\nTesting config import...")
try:
    from config import settings
    print("✅ Config imported successfully")
    print(f"Alpaca API Key: {settings.ALPACA_API_KEY[:10]}...")
    print(f"Environment: {settings.ENVIRONMENT}")
except Exception as e:
    print(f"❌ Config import failed: {e}")
