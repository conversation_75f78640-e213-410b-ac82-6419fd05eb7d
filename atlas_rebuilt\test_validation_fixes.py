#!/usr/bin/env python3
"""
Test validation fixes for A.T.L.A.S components
"""

import os
import sys
import asyncio

# Set validation mode
os.environ['VALIDATION_MODE'] = 'true'

async def test_component_initialization():
    """Test that components can initialize in validation mode"""
    print("🔧 Testing A.T.L.A.S Component Initialization in Validation Mode")
    print("=" * 70)
    
    results = {}
    
    # Test 1: Configuration
    try:
        from config import settings, validate_environment
        print(f"✅ Configuration loaded - Validation mode: {settings.VALIDATION_MODE}")
        
        env_status = validate_environment()
        print(f"   Environment valid: {env_status['valid']}")
        print(f"   Errors: {len(env_status['errors'])}")
        print(f"   Warnings: {len(env_status['warnings'])}")
        results["config"] = "✅ PASSED"
    except Exception as e:
        print(f"❌ Configuration failed: {e}")
        results["config"] = f"❌ FAILED: {e}"
    
    # Test 2: Orchestrator
    try:
        from atlas_orchestrator import AtlasOrchestrator
        orchestrator = AtlasOrchestrator()
        print(f"✅ Orchestrator created - Validation mode: {orchestrator.validation_mode}")
        results["orchestrator"] = "✅ PASSED"
    except Exception as e:
        print(f"❌ Orchestrator failed: {e}")
        results["orchestrator"] = f"❌ FAILED: {e}"
    
    # Test 3: AI Engine
    try:
        from atlas_ai_engine import AtlasAIEngine
        ai_engine = AtlasAIEngine()
        print(f"✅ AI Engine created - Validation mode: {ai_engine.validation_mode}")
        results["ai_engine"] = "✅ PASSED"
    except Exception as e:
        print(f"❌ AI Engine failed: {e}")
        results["ai_engine"] = f"❌ FAILED: {e}"
    
    # Test 4: Trading Engine
    try:
        from atlas_trading_engine import AtlasTradingEngine
        trading_engine = AtlasTradingEngine()
        print(f"✅ Trading Engine created - Validation mode: {trading_engine.validation_mode}")
        results["trading_engine"] = "✅ PASSED"
    except Exception as e:
        print(f"❌ Trading Engine failed: {e}")
        results["trading_engine"] = f"❌ FAILED: {e}"
    
    # Test 5: Market Engine
    try:
        from atlas_market_engine import AtlasMarketEngine
        market_engine = AtlasMarketEngine()
        print(f"✅ Market Engine created - Validation mode: {market_engine.validation_mode}")
        results["market_engine"] = "✅ PASSED"
    except Exception as e:
        print(f"❌ Market Engine failed: {e}")
        results["market_engine"] = f"❌ FAILED: {e}"
    
    # Test 6: Component Initialization
    if "orchestrator" in results and "✅" in results["orchestrator"]:
        try:
            # Test orchestrator initialization with validation mode
            def mock_progress(component, progress, status, message):
                print(f"   {component}: {progress:.0%} - {message}")
            
            await orchestrator.initialize_with_progress(mock_progress)
            print("✅ Orchestrator initialization completed")
            results["initialization"] = "✅ PASSED"
        except Exception as e:
            print(f"❌ Orchestrator initialization failed: {e}")
            results["initialization"] = f"❌ FAILED: {e}"
    
    # Summary
    print("\n" + "=" * 70)
    print("🎯 VALIDATION FIXES TEST SUMMARY")
    print("=" * 70)
    
    passed = sum(1 for result in results.values() if "✅" in result)
    total = len(results)
    
    print(f"Tests Passed: {passed}/{total}")
    print(f"Success Rate: {passed/total*100:.1f}%")
    
    print("\nDetailed Results:")
    for component, result in results.items():
        print(f"  {component}: {result}")
    
    if passed == total:
        print("\n🎉 ALL VALIDATION FIXES WORKING!")
        return True
    else:
        print(f"\n⚠️  {total-passed} issues remaining")
        return False

if __name__ == "__main__":
    try:
        success = asyncio.run(test_component_initialization())
        sys.exit(0 if success else 1)
    except Exception as e:
        print(f"💥 Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
