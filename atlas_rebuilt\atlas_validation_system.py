"""
A.T.L.A.S Validation System - Comprehensive System Verification
Validates all components, features, documentation, and integrations
"""

import asyncio
import logging
import json
import os
import re
import inspect
from datetime import datetime
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass, asdict
from enum import Enum
import importlib.util

logger = logging.getLogger(__name__)


class ValidationStatus(Enum):
    """Validation test status"""
    PASSED = "passed"
    FAILED = "failed"
    WARNING = "warning"
    SKIPPED = "skipped"
    ERROR = "error"


class ValidationCategory(Enum):
    """Categories of validation tests"""
    FEATURE_COVERAGE = "feature_coverage"
    API_ENDPOINTS = "api_endpoints"
    INTEGRATION = "integration"
    CRITICAL_PATH = "critical_path"
    SAFETY_RISK = "safety_risk"
    DOCUMENTATION = "documentation"
    DEPENDENCIES = "dependencies"


@dataclass
class ValidationResult:
    """Individual validation test result"""
    test_name: str
    category: ValidationCategory
    status: ValidationStatus
    message: str
    details: Dict[str, Any]
    execution_time: float
    timestamp: str


@dataclass
class ValidationReport:
    """Comprehensive validation report"""
    system_name: str
    validation_timestamp: str
    total_tests: int
    passed_tests: int
    failed_tests: int
    warning_tests: int
    skipped_tests: int
    error_tests: int
    overall_status: ValidationStatus
    results_by_category: Dict[ValidationCategory, List[ValidationResult]]
    summary: Dict[str, Any]
    recommendations: List[str]


class AtlasValidationSystem:
    """Comprehensive validation system for A.T.L.A.S AI Trading System"""
    
    def __init__(self, atlas_root_path: str = "."):
        self.atlas_root = atlas_root_path
        self.results: List[ValidationResult] = []
        self.validators: Dict[ValidationCategory, List[Callable]] = {
            ValidationCategory.FEATURE_COVERAGE: [],
            ValidationCategory.API_ENDPOINTS: [],
            ValidationCategory.INTEGRATION: [],
            ValidationCategory.CRITICAL_PATH: [],
            ValidationCategory.SAFETY_RISK: [],
            ValidationCategory.DOCUMENTATION: [],
            ValidationCategory.DEPENDENCIES: []
        }
        
        # System components to validate
        self.components = {
            "orchestrator": None,
            "ai_engine": None,
            "trading_engine": None,
            "market_engine": None,
            "education_engine": None,
            "risk_engine": None,
            "server": None
        }
        
        logger.info("🔍 A.T.L.A.S Validation System initialized")
    
    def register_validator(self, category: ValidationCategory, validator_func: Callable):
        """Register a validation function for a specific category"""
        self.validators[category].append(validator_func)
        logger.debug(f"Registered validator {validator_func.__name__} for {category.value}")
    
    async def run_full_validation(self) -> ValidationReport:
        """Run complete validation suite and generate comprehensive report"""
        logger.info("🚀 Starting comprehensive A.T.L.A.S validation...")
        start_time = datetime.now()
        
        # Clear previous results
        self.results = []
        
        # Initialize system components
        await self._initialize_components()
        
        # Run all validation categories
        for category in ValidationCategory:
            logger.info(f"🔍 Running {category.value} validation...")
            await self._run_category_validation(category)
        
        # Generate comprehensive report
        report = self._generate_report(start_time)
        
        # Save report to file
        await self._save_report(report)
        
        logger.info(f"✅ Validation complete: {report.passed_tests}/{report.total_tests} tests passed")
        return report
    
    async def _initialize_components(self):
        """Initialize A.T.L.A.S system components for testing"""
        try:
            # Ensure VALIDATION_MODE is set before importing components
            import os
            if not os.environ.get('VALIDATION_MODE'):
                os.environ['VALIDATION_MODE'] = 'true'

            # Try to import and initialize components
            logger.info("🔧 Initializing A.T.L.A.S components for validation...")

            # Import orchestrator
            try:
                from atlas_orchestrator import AtlasOrchestrator
                self.components["orchestrator"] = AtlasOrchestrator()
                logger.info("✅ Orchestrator imported successfully")
            except Exception as e:
                logger.warning(f"⚠️ Orchestrator import failed: {e}")

            # Import individual engines
            component_imports = {
                "ai_engine": ("atlas_ai_engine", "AtlasAIEngine"),
                "trading_engine": ("atlas_trading_engine", "AtlasTradingEngine"),
                "market_engine": ("atlas_market_engine", "AtlasMarketEngine"),
                "education_engine": ("atlas_education_engine", "AtlasEducationEngine"),
                "risk_engine": ("atlas_risk_engine", "AtlasRiskEngine")
            }

            for component_name, (module_name, class_name) in component_imports.items():
                try:
                    module = importlib.import_module(module_name)
                    component_class = getattr(module, class_name)
                    self.components[component_name] = component_class()
                    logger.info(f"✅ {component_name} imported successfully")
                except Exception as e:
                    logger.warning(f"⚠️ {component_name} import failed: {e}")

        except Exception as e:
            logger.error(f"❌ Component initialization failed: {e}")
    
    async def _run_category_validation(self, category: ValidationCategory):
        """Run all validators for a specific category"""
        validators = self.validators[category]
        
        if not validators:
            # Add default validators if none registered
            await self._add_default_validators(category)
            validators = self.validators[category]
        
        for validator in validators:
            try:
                start_time = datetime.now()
                result = await validator(self)
                execution_time = (datetime.now() - start_time).total_seconds()
                
                if isinstance(result, ValidationResult):
                    result.execution_time = execution_time
                    self.results.append(result)
                else:
                    # Convert simple result to ValidationResult
                    status = ValidationStatus.PASSED if result else ValidationStatus.FAILED
                    validation_result = ValidationResult(
                        test_name=validator.__name__,
                        category=category,
                        status=status,
                        message=f"Validator {validator.__name__} {'passed' if result else 'failed'}",
                        details={},
                        execution_time=execution_time,
                        timestamp=datetime.now().isoformat()
                    )
                    self.results.append(validation_result)
                    
            except Exception as e:
                logger.error(f"❌ Validator {validator.__name__} failed: {e}")
                error_result = ValidationResult(
                    test_name=validator.__name__,
                    category=category,
                    status=ValidationStatus.ERROR,
                    message=f"Validator execution error: {str(e)}",
                    details={"error": str(e)},
                    execution_time=0.0,
                    timestamp=datetime.now().isoformat()
                )
                self.results.append(error_result)
    
    async def _add_default_validators(self, category: ValidationCategory):
        """Add default validators for each category"""
        if category == ValidationCategory.FEATURE_COVERAGE:
            self.register_validator(category, self._validate_feature_coverage)
        elif category == ValidationCategory.API_ENDPOINTS:
            self.register_validator(category, self._validate_api_endpoints)
        elif category == ValidationCategory.INTEGRATION:
            self.register_validator(category, self._validate_integration)
        elif category == ValidationCategory.CRITICAL_PATH:
            self.register_validator(category, self._validate_critical_paths)
        elif category == ValidationCategory.SAFETY_RISK:
            self.register_validator(category, self._validate_safety_risk)
        elif category == ValidationCategory.DOCUMENTATION:
            self.register_validator(category, self._validate_documentation)
        elif category == ValidationCategory.DEPENDENCIES:
            self.register_validator(category, self._validate_dependencies)
    
    def _generate_report(self, start_time: datetime) -> ValidationReport:
        """Generate comprehensive validation report"""
        total_time = (datetime.now() - start_time).total_seconds()
        
        # Count results by status
        status_counts = {status: 0 for status in ValidationStatus}
        for result in self.results:
            status_counts[result.status] += 1
        
        # Group results by category
        results_by_category = {category: [] for category in ValidationCategory}
        for result in self.results:
            results_by_category[result.category].append(result)
        
        # Determine overall status
        if status_counts[ValidationStatus.FAILED] > 0 or status_counts[ValidationStatus.ERROR] > 0:
            overall_status = ValidationStatus.FAILED
        elif status_counts[ValidationStatus.WARNING] > 0:
            overall_status = ValidationStatus.WARNING
        else:
            overall_status = ValidationStatus.PASSED
        
        # Generate recommendations
        recommendations = self._generate_recommendations()
        
        # Create summary
        summary = {
            "total_execution_time": total_time,
            "success_rate": (status_counts[ValidationStatus.PASSED] / len(self.results) * 100) if self.results else 0,
            "critical_issues": status_counts[ValidationStatus.FAILED] + status_counts[ValidationStatus.ERROR],
            "warnings": status_counts[ValidationStatus.WARNING],
            "components_tested": len([c for c in self.components.values() if c is not None]),
            "categories_tested": len([cat for cat in ValidationCategory if results_by_category[cat]])
        }
        
        return ValidationReport(
            system_name="A.T.L.A.S AI Trading System",
            validation_timestamp=datetime.now().isoformat(),
            total_tests=len(self.results),
            passed_tests=status_counts[ValidationStatus.PASSED],
            failed_tests=status_counts[ValidationStatus.FAILED],
            warning_tests=status_counts[ValidationStatus.WARNING],
            skipped_tests=status_counts[ValidationStatus.SKIPPED],
            error_tests=status_counts[ValidationStatus.ERROR],
            overall_status=overall_status,
            results_by_category=results_by_category,
            summary=summary,
            recommendations=recommendations
        )
    
    def _generate_recommendations(self) -> List[str]:
        """Generate recommendations based on validation results"""
        recommendations = []
        
        # Analyze failed tests
        failed_results = [r for r in self.results if r.status == ValidationStatus.FAILED]
        if failed_results:
            recommendations.append(f"Address {len(failed_results)} critical failures before deployment")
        
        # Analyze warnings
        warning_results = [r for r in self.results if r.status == ValidationStatus.WARNING]
        if warning_results:
            recommendations.append(f"Review {len(warning_results)} warnings for potential improvements")
        
        # Check component availability
        missing_components = [name for name, comp in self.components.items() if comp is None]
        if missing_components:
            recommendations.append(f"Initialize missing components: {', '.join(missing_components)}")
        
        return recommendations
    
    async def _save_report(self, report: ValidationReport):
        """Save validation report to file"""
        try:
            # Convert report to dictionary manually to handle enums properly
            report_data = {
                "system_name": report.system_name,
                "validation_timestamp": report.validation_timestamp,
                "total_tests": report.total_tests,
                "passed_tests": report.passed_tests,
                "failed_tests": report.failed_tests,
                "warning_tests": report.warning_tests,
                "skipped_tests": report.skipped_tests,
                "error_tests": report.error_tests,
                "overall_status": report.overall_status.value,
                "summary": report.summary,
                "recommendations": report.recommendations,
                "results_by_category": {}
            }

            # Convert results by category
            for category, results in report.results_by_category.items():
                category_results = []
                for result in results:
                    category_results.append({
                        "test_name": result.test_name,
                        "category": result.category.value,
                        "status": result.status.value,
                        "message": result.message,
                        "details": result.details,
                        "execution_time": result.execution_time,
                        "timestamp": result.timestamp
                    })
                report_data["results_by_category"][category.value] = category_results

            # Save to file
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"atlas_validation_report_{timestamp}.json"

            with open(filename, 'w') as f:
                json.dump(report_data, f, indent=2)

            logger.info(f"📄 Validation report saved to {filename}")

        except Exception as e:
            logger.error(f"❌ Failed to save validation report: {e}")

    # ===== VALIDATION METHODS =====

    async def _validate_feature_coverage(self, validator_instance) -> ValidationResult:
        """Validate that all documented features are implemented"""
        try:
            logger.info("🔍 Validating feature coverage...")

            # Read README.md to extract documented features
            readme_path = os.path.join(self.atlas_root, "README.md")
            documented_features = []
            implemented_features = []

            if os.path.exists(readme_path):
                with open(readme_path, 'r', encoding='utf-8') as f:
                    readme_content = f.read()

                # Extract features from README
                documented_features = self._extract_documented_features(readme_content)

            # Check implemented features in codebase
            implemented_features = await self._scan_implemented_features()

            # Compare documented vs implemented
            missing_implementations = []
            undocumented_features = []

            for feature in documented_features:
                if not self._is_feature_implemented(feature, implemented_features):
                    missing_implementations.append(feature)

            for feature in implemented_features:
                if not self._is_feature_documented(feature, documented_features):
                    undocumented_features.append(feature)

            # Determine status
            if missing_implementations:
                status = ValidationStatus.FAILED
                message = f"Missing implementations: {len(missing_implementations)} features"
            elif undocumented_features:
                status = ValidationStatus.WARNING
                message = f"Undocumented features: {len(undocumented_features)} features"
            else:
                status = ValidationStatus.PASSED
                message = "All documented features are implemented"

            return ValidationResult(
                test_name="feature_coverage_validation",
                category=ValidationCategory.FEATURE_COVERAGE,
                status=status,
                message=message,
                details={
                    "documented_features": documented_features,
                    "implemented_features": implemented_features,
                    "missing_implementations": missing_implementations,
                    "undocumented_features": undocumented_features,
                    "coverage_percentage": ((len(documented_features) - len(missing_implementations)) / len(documented_features) * 100) if documented_features else 100
                },
                execution_time=0.0,
                timestamp=datetime.now().isoformat()
            )

        except Exception as e:
            logger.error(f"Feature coverage validation error: {e}")
            return ValidationResult(
                test_name="feature_coverage_validation",
                category=ValidationCategory.FEATURE_COVERAGE,
                status=ValidationStatus.ERROR,
                message=f"Validation error: {str(e)}",
                details={"error": str(e)},
                execution_time=0.0,
                timestamp=datetime.now().isoformat()
            )

    def _extract_documented_features(self, readme_content: str) -> List[str]:
        """Extract documented features from README content"""
        features = []

        # Look for feature sections and bullet points
        feature_patterns = [
            r'## Features\s*\n(.*?)(?=\n##|\n#|\Z)',
            r'### Key Features\s*\n(.*?)(?=\n##|\n#|\Z)',
            r'## Core Features\s*\n(.*?)(?=\n##|\n#|\Z)',
        ]

        for pattern in feature_patterns:
            matches = re.findall(pattern, readme_content, re.DOTALL | re.IGNORECASE)
            for match in matches:
                # Extract bullet points
                bullet_points = re.findall(r'[-*+]\s*\*\*(.*?)\*\*', match)
                features.extend(bullet_points)

                # Also extract simple bullet points
                simple_bullets = re.findall(r'[-*+]\s*([^*\n]+)', match)
                features.extend([b.strip() for b in simple_bullets if not b.strip().startswith('**')])

        # Clean and deduplicate
        features = [f.strip() for f in features if f.strip()]
        return list(set(features))

    async def _scan_implemented_features(self) -> List[str]:
        """Scan codebase for implemented features"""
        features = []

        # Scan Python files for class methods and functions
        python_files = [
            "atlas_ai_engine.py",
            "atlas_trading_engine.py",
            "atlas_market_engine.py",
            "atlas_education_engine.py",
            "atlas_risk_engine.py",
            "atlas_orchestrator.py",
            "atlas_server.py"
        ]

        for filename in python_files:
            filepath = os.path.join(self.atlas_root, filename)
            if os.path.exists(filepath):
                try:
                    with open(filepath, 'r', encoding='utf-8') as f:
                        content = f.read()

                    # Extract class methods and functions
                    methods = re.findall(r'def\s+([a-zA-Z_][a-zA-Z0-9_]*)', content)
                    features.extend(methods)

                    # Extract API endpoints
                    endpoints = re.findall(r'@app\.(get|post|put|delete)\("([^"]+)"', content)
                    features.extend([endpoint[1] for endpoint in endpoints])

                except Exception as e:
                    logger.warning(f"Error scanning {filename}: {e}")

        return list(set(features))

    def _is_feature_implemented(self, documented_feature: str, implemented_features: List[str]) -> bool:
        """Check if a documented feature is implemented"""
        # Simple keyword matching - could be enhanced with more sophisticated matching
        feature_lower = documented_feature.lower()

        for impl_feature in implemented_features:
            if any(keyword in impl_feature.lower() for keyword in feature_lower.split()):
                return True

        return False

    def _is_feature_documented(self, implemented_feature: str, documented_features: List[str]) -> bool:
        """Check if an implemented feature is documented"""
        feature_lower = implemented_feature.lower()

        for doc_feature in documented_features:
            if any(keyword in doc_feature.lower() for keyword in feature_lower.split('_')):
                return True

        return False

    async def _validate_api_endpoints(self, validator_instance) -> ValidationResult:
        """Validate that all documented API endpoints are functional"""
        try:
            logger.info("🔍 Validating API endpoints...")

            # Extract API endpoints from server file
            server_path = os.path.join(self.atlas_root, "atlas_server.py")
            documented_endpoints = []
            implemented_endpoints = []

            if os.path.exists(server_path):
                with open(server_path, 'r', encoding='utf-8') as f:
                    server_content = f.read()

                # Extract FastAPI endpoints
                endpoint_patterns = [
                    r'@app\.get\("([^"]+)"\)',
                    r'@app\.post\("([^"]+)"\)',
                    r'@app\.put\("([^"]+)"\)',
                    r'@app\.delete\("([^"]+)"\)'
                ]

                for pattern in endpoint_patterns:
                    matches = re.findall(pattern, server_content)
                    implemented_endpoints.extend(matches)

            # Check README for documented endpoints
            readme_path = os.path.join(self.atlas_root, "README.md")
            if os.path.exists(readme_path):
                with open(readme_path, 'r', encoding='utf-8') as f:
                    readme_content = f.read()

                # Extract API endpoints from documentation
                api_patterns = [
                    r'`(GET|POST|PUT|DELETE)\s+([^`]+)`',
                    r'`([^`]*api[^`]*)`'
                ]

                for pattern in api_patterns:
                    matches = re.findall(pattern, readme_content)
                    if isinstance(matches[0], tuple) if matches else False:
                        documented_endpoints.extend([match[1] for match in matches])
                    else:
                        documented_endpoints.extend(matches)

            # Compare documented vs implemented
            missing_endpoints = []
            undocumented_endpoints = []

            for endpoint in documented_endpoints:
                if endpoint not in implemented_endpoints:
                    missing_endpoints.append(endpoint)

            for endpoint in implemented_endpoints:
                if endpoint not in documented_endpoints:
                    undocumented_endpoints.append(endpoint)

            # Determine status
            if missing_endpoints:
                status = ValidationStatus.FAILED
                message = f"Missing endpoint implementations: {len(missing_endpoints)}"
            elif undocumented_endpoints:
                status = ValidationStatus.WARNING
                message = f"Undocumented endpoints: {len(undocumented_endpoints)}"
            else:
                status = ValidationStatus.PASSED
                message = "All documented endpoints are implemented"

            return ValidationResult(
                test_name="api_endpoints_validation",
                category=ValidationCategory.API_ENDPOINTS,
                status=status,
                message=message,
                details={
                    "documented_endpoints": documented_endpoints,
                    "implemented_endpoints": implemented_endpoints,
                    "missing_endpoints": missing_endpoints,
                    "undocumented_endpoints": undocumented_endpoints,
                    "total_endpoints": len(implemented_endpoints)
                },
                execution_time=0.0,
                timestamp=datetime.now().isoformat()
            )

        except Exception as e:
            logger.error(f"API endpoints validation error: {e}")
            return ValidationResult(
                test_name="api_endpoints_validation",
                category=ValidationCategory.API_ENDPOINTS,
                status=ValidationStatus.ERROR,
                message=f"Validation error: {str(e)}",
                details={"error": str(e)},
                execution_time=0.0,
                timestamp=datetime.now().isoformat()
            )

    async def _validate_integration(self, validator_instance) -> ValidationResult:
        """Validate that all major system components are properly integrated"""
        try:
            logger.info("🔍 Validating system integration...")

            integration_tests = []
            failed_integrations = []

            # Test orchestrator integration
            if self.components["orchestrator"]:
                try:
                    orchestrator = self.components["orchestrator"]

                    # Check if orchestrator has all required engines
                    required_engines = ["ai_engine", "trading_engine", "market_engine", "education_engine", "risk_engine"]
                    for engine_name in required_engines:
                        if hasattr(orchestrator, engine_name):
                            integration_tests.append(f"orchestrator.{engine_name}")
                        else:
                            failed_integrations.append(f"orchestrator.{engine_name}")

                except Exception as e:
                    failed_integrations.append(f"orchestrator_initialization: {str(e)}")
            else:
                failed_integrations.append("orchestrator_not_available")

            # Test component initialization
            for component_name, component in self.components.items():
                if component:
                    try:
                        # Check if component has initialize method
                        if hasattr(component, 'initialize'):
                            integration_tests.append(f"{component_name}_has_initialize")

                        # Check if component has required methods
                        if component_name == "ai_engine" and hasattr(component, 'process_message'):
                            integration_tests.append(f"{component_name}_has_process_message")
                        elif component_name == "trading_engine" and hasattr(component, 'place_order'):
                            integration_tests.append(f"{component_name}_has_place_order")
                        elif component_name == "market_engine" and hasattr(component, 'get_quote'):
                            integration_tests.append(f"{component_name}_has_get_quote")

                    except Exception as e:
                        failed_integrations.append(f"{component_name}_validation: {str(e)}")
                else:
                    failed_integrations.append(f"{component_name}_not_available")

            # Determine status
            if failed_integrations:
                status = ValidationStatus.FAILED
                message = f"Integration failures: {len(failed_integrations)}"
            else:
                status = ValidationStatus.PASSED
                message = "All components properly integrated"

            return ValidationResult(
                test_name="integration_validation",
                category=ValidationCategory.INTEGRATION,
                status=status,
                message=message,
                details={
                    "successful_integrations": integration_tests,
                    "failed_integrations": failed_integrations,
                    "components_available": len([c for c in self.components.values() if c is not None]),
                    "total_components": len(self.components)
                },
                execution_time=0.0,
                timestamp=datetime.now().isoformat()
            )

        except Exception as e:
            logger.error(f"Integration validation error: {e}")
            return ValidationResult(
                test_name="integration_validation",
                category=ValidationCategory.INTEGRATION,
                status=ValidationStatus.ERROR,
                message=f"Validation error: {str(e)}",
                details={"error": str(e)},
                execution_time=0.0,
                timestamp=datetime.now().isoformat()
            )

    async def _validate_critical_paths(self, validator_instance) -> ValidationResult:
        """Test core user workflows"""
        try:
            logger.info("🔍 Validating critical user paths...")

            critical_path_tests = []
            failed_paths = []

            # Test 1: Chat message processing
            try:
                if self.components["ai_engine"]:
                    ai_engine = self.components["ai_engine"]
                    if hasattr(ai_engine, 'process_message'):
                        critical_path_tests.append("chat_message_processing_available")
                    else:
                        failed_paths.append("chat_message_processing_missing")
                else:
                    failed_paths.append("ai_engine_not_available")
            except Exception as e:
                failed_paths.append(f"chat_processing_test: {str(e)}")

            # Test 2: TTM Squeeze signal generation
            try:
                if self.components["market_engine"]:
                    market_engine = self.components["market_engine"]
                    if hasattr(market_engine, 'get_ttm_squeeze_signals'):
                        critical_path_tests.append("ttm_squeeze_signals_available")
                    else:
                        failed_paths.append("ttm_squeeze_signals_missing")
                else:
                    failed_paths.append("market_engine_not_available")
            except Exception as e:
                failed_paths.append(f"ttm_squeeze_test: {str(e)}")

            # Test 3: Trade execution confirmation
            try:
                if self.components["trading_engine"]:
                    trading_engine = self.components["trading_engine"]
                    if hasattr(trading_engine, 'prepare_trade_for_confirmation'):
                        critical_path_tests.append("trade_confirmation_available")
                    else:
                        failed_paths.append("trade_confirmation_missing")
                else:
                    failed_paths.append("trading_engine_not_available")
            except Exception as e:
                failed_paths.append(f"trade_confirmation_test: {str(e)}")

            # Test 4: Portfolio management
            try:
                if self.components["trading_engine"]:
                    trading_engine = self.components["trading_engine"]
                    if hasattr(trading_engine, 'get_portfolio_summary'):
                        critical_path_tests.append("portfolio_management_available")
                    else:
                        failed_paths.append("portfolio_management_missing")
            except Exception as e:
                failed_paths.append(f"portfolio_management_test: {str(e)}")

            # Test 5: Educational query processing
            try:
                if self.components["education_engine"]:
                    education_engine = self.components["education_engine"]
                    if hasattr(education_engine, 'process_query'):
                        critical_path_tests.append("education_query_available")
                    else:
                        failed_paths.append("education_query_missing")
                else:
                    failed_paths.append("education_engine_not_available")
            except Exception as e:
                failed_paths.append(f"education_query_test: {str(e)}")

            # Determine status
            if failed_paths:
                status = ValidationStatus.FAILED
                message = f"Critical path failures: {len(failed_paths)}"
            else:
                status = ValidationStatus.PASSED
                message = "All critical paths functional"

            return ValidationResult(
                test_name="critical_paths_validation",
                category=ValidationCategory.CRITICAL_PATH,
                status=status,
                message=message,
                details={
                    "successful_paths": critical_path_tests,
                    "failed_paths": failed_paths,
                    "total_paths_tested": len(critical_path_tests) + len(failed_paths)
                },
                execution_time=0.0,
                timestamp=datetime.now().isoformat()
            )

        except Exception as e:
            logger.error(f"Critical paths validation error: {e}")
            return ValidationResult(
                test_name="critical_paths_validation",
                category=ValidationCategory.CRITICAL_PATH,
                status=ValidationStatus.ERROR,
                message=f"Validation error: {str(e)}",
                details={"error": str(e)},
                execution_time=0.0,
                timestamp=datetime.now().isoformat()
            )

    async def _validate_safety_risk(self, validator_instance) -> ValidationResult:
        """Validate safety guardrails and risk management protocols"""
        try:
            logger.info("🔍 Validating safety and risk management...")

            safety_tests = []
            safety_failures = []

            # Test trading engine safety features
            if self.components["trading_engine"]:
                trading_engine = self.components["trading_engine"]

                # Check for trade confirmation system
                if hasattr(trading_engine, 'prepare_trade_for_confirmation'):
                    safety_tests.append("trade_confirmation_system")
                else:
                    safety_failures.append("trade_confirmation_system_missing")

                # Check for execution safeguards
                if hasattr(trading_engine, 'execution_safeguards'):
                    safety_tests.append("execution_safeguards")
                else:
                    safety_failures.append("execution_safeguards_missing")

                # Check for portfolio risk analysis
                if hasattr(trading_engine, 'analyze_portfolio_risk'):
                    safety_tests.append("portfolio_risk_analysis")
                else:
                    safety_failures.append("portfolio_risk_analysis_missing")

                # Check for position sizing limits
                if hasattr(trading_engine, 'portfolio_optimization_settings'):
                    safety_tests.append("position_sizing_limits")
                else:
                    safety_failures.append("position_sizing_limits_missing")
            else:
                safety_failures.append("trading_engine_not_available")

            # Test risk engine features
            if self.components["risk_engine"]:
                risk_engine = self.components["risk_engine"]

                # Check for risk assessment
                if hasattr(risk_engine, 'assess_trade_risk'):
                    safety_tests.append("trade_risk_assessment")
                else:
                    safety_failures.append("trade_risk_assessment_missing")

                # Check for portfolio risk monitoring
                if hasattr(risk_engine, 'monitor_portfolio_risk'):
                    safety_tests.append("portfolio_risk_monitoring")
                else:
                    safety_failures.append("portfolio_risk_monitoring_missing")
            else:
                safety_failures.append("risk_engine_not_available")

            # Test AI engine safety features
            if self.components["ai_engine"]:
                ai_engine = self.components["ai_engine"]

                # Check for emotional analysis
                if hasattr(ai_engine, '_parse_trading_goals'):
                    safety_tests.append("goal_reality_checking")
                else:
                    safety_failures.append("goal_reality_checking_missing")

            # Determine status
            if safety_failures:
                status = ValidationStatus.FAILED
                message = f"Safety system failures: {len(safety_failures)}"
            else:
                status = ValidationStatus.PASSED
                message = "All safety systems operational"

            return ValidationResult(
                test_name="safety_risk_validation",
                category=ValidationCategory.SAFETY_RISK,
                status=status,
                message=message,
                details={
                    "safety_systems_operational": safety_tests,
                    "safety_system_failures": safety_failures,
                    "safety_coverage": (len(safety_tests) / (len(safety_tests) + len(safety_failures)) * 100) if (safety_tests or safety_failures) else 0
                },
                execution_time=0.0,
                timestamp=datetime.now().isoformat()
            )

        except Exception as e:
            logger.error(f"Safety/risk validation error: {e}")
            return ValidationResult(
                test_name="safety_risk_validation",
                category=ValidationCategory.SAFETY_RISK,
                status=ValidationStatus.ERROR,
                message=f"Validation error: {str(e)}",
                details={"error": str(e)},
                execution_time=0.0,
                timestamp=datetime.now().isoformat()
            )

    async def _validate_documentation(self, validator_instance) -> ValidationResult:
        """Validate documentation accuracy and completeness"""
        try:
            logger.info("🔍 Validating documentation accuracy...")

            doc_tests = []
            doc_issues = []

            # Check README.md exists and has required sections
            readme_path = os.path.join(self.atlas_root, "README.md")
            if os.path.exists(readme_path):
                with open(readme_path, 'r', encoding='utf-8') as f:
                    readme_content = f.read()

                # Check for required sections
                required_sections = [
                    "Installation", "Quick Start", "Features", "API", "Configuration"
                ]

                for section in required_sections:
                    if section.lower() in readme_content.lower():
                        doc_tests.append(f"readme_has_{section.lower()}")
                    else:
                        doc_issues.append(f"readme_missing_{section.lower()}")

                # Check for API documentation
                if "api" in readme_content.lower() and "endpoint" in readme_content.lower():
                    doc_tests.append("api_documentation_present")
                else:
                    doc_issues.append("api_documentation_missing")

                # Check for installation instructions
                if any(keyword in readme_content.lower() for keyword in ["pip install", "requirements.txt", "conda"]):
                    doc_tests.append("installation_instructions_present")
                else:
                    doc_issues.append("installation_instructions_missing")

            else:
                doc_issues.append("readme_file_missing")

            # Check for requirements.txt
            requirements_path = os.path.join(self.atlas_root, "requirements.txt")
            if os.path.exists(requirements_path):
                doc_tests.append("requirements_file_present")
            else:
                doc_issues.append("requirements_file_missing")

            # Check for configuration documentation
            config_path = os.path.join(self.atlas_root, "config.py")
            if os.path.exists(config_path):
                doc_tests.append("config_file_present")
            else:
                doc_issues.append("config_file_missing")

            # Determine status
            if doc_issues:
                status = ValidationStatus.WARNING if len(doc_issues) < len(doc_tests) else ValidationStatus.FAILED
                message = f"Documentation issues: {len(doc_issues)}"
            else:
                status = ValidationStatus.PASSED
                message = "Documentation complete and accurate"

            return ValidationResult(
                test_name="documentation_validation",
                category=ValidationCategory.DOCUMENTATION,
                status=status,
                message=message,
                details={
                    "documentation_complete": doc_tests,
                    "documentation_issues": doc_issues,
                    "completeness_score": (len(doc_tests) / (len(doc_tests) + len(doc_issues)) * 100) if (doc_tests or doc_issues) else 0
                },
                execution_time=0.0,
                timestamp=datetime.now().isoformat()
            )

        except Exception as e:
            logger.error(f"Documentation validation error: {e}")
            return ValidationResult(
                test_name="documentation_validation",
                category=ValidationCategory.DOCUMENTATION,
                status=ValidationStatus.ERROR,
                message=f"Validation error: {str(e)}",
                details={"error": str(e)},
                execution_time=0.0,
                timestamp=datetime.now().isoformat()
            )

    async def _validate_dependencies(self, validator_instance) -> ValidationResult:
        """Validate dependencies and configuration"""
        try:
            logger.info("🔍 Validating dependencies and configuration...")

            dependency_tests = []
            dependency_issues = []

            # Check required Python packages
            required_packages = [
                "fastapi", "uvicorn", "aiohttp", "openai", "numpy", "pandas"
            ]

            for package in required_packages:
                try:
                    importlib.import_module(package)
                    dependency_tests.append(f"package_{package}_available")
                except ImportError:
                    dependency_issues.append(f"package_{package}_missing")

            # Check optional packages with graceful fallbacks
            optional_packages = [
                ("transformers", "ML sentiment analysis"),
                ("chromadb", "RAG system"),
                ("googleapiclient", "Google search"),
                ("duckduckgo_search", "DuckDuckGo search")
            ]

            for package, description in optional_packages:
                try:
                    importlib.import_module(package)
                    dependency_tests.append(f"optional_{package}_available")
                except ImportError:
                    dependency_tests.append(f"optional_{package}_fallback_available")

            # Check configuration files
            config_files = ["config.py", "models.py"]
            for config_file in config_files:
                config_path = os.path.join(self.atlas_root, config_file)
                if os.path.exists(config_path):
                    dependency_tests.append(f"config_{config_file}_present")
                else:
                    dependency_issues.append(f"config_{config_file}_missing")

            # Check environment variables or API keys
            try:
                from config import get_api_config

                # Test API configurations
                api_configs = ["openai", "alpaca", "fmp", "predicto"]
                for api_name in api_configs:
                    try:
                        config = get_api_config(api_name)
                        if config and config.get("api_key"):
                            dependency_tests.append(f"api_config_{api_name}_present")
                        else:
                            dependency_issues.append(f"api_config_{api_name}_missing")
                    except Exception:
                        dependency_issues.append(f"api_config_{api_name}_error")

            except ImportError:
                dependency_issues.append("config_module_import_failed")

            # Check for graceful fallbacks
            fallback_tests = []

            # Check if components handle missing dependencies gracefully
            for component_name, component in self.components.items():
                if component:
                    # Check if component has proper error handling
                    try:
                        # Look for try/except blocks in component initialization
                        component_file = f"atlas_{component_name}.py"
                        component_path = os.path.join(self.atlas_root, component_file)

                        if os.path.exists(component_path):
                            with open(component_path, 'r', encoding='utf-8') as f:
                                content = f.read()

                            if "try:" in content and "except" in content:
                                fallback_tests.append(f"{component_name}_has_error_handling")
                            else:
                                dependency_issues.append(f"{component_name}_missing_error_handling")
                    except Exception:
                        pass

            # Determine status
            critical_missing = [issue for issue in dependency_issues if "missing" in issue and "optional" not in issue]

            if critical_missing:
                status = ValidationStatus.FAILED
                message = f"Critical dependencies missing: {len(critical_missing)}"
            elif dependency_issues:
                status = ValidationStatus.WARNING
                message = f"Some dependencies missing: {len(dependency_issues)}"
            else:
                status = ValidationStatus.PASSED
                message = "All dependencies properly configured"

            return ValidationResult(
                test_name="dependencies_validation",
                category=ValidationCategory.DEPENDENCIES,
                status=status,
                message=message,
                details={
                    "dependencies_satisfied": dependency_tests,
                    "dependency_issues": dependency_issues,
                    "fallback_mechanisms": fallback_tests,
                    "critical_missing": critical_missing,
                    "dependency_coverage": (len(dependency_tests) / (len(dependency_tests) + len(dependency_issues)) * 100) if (dependency_tests or dependency_issues) else 0
                },
                execution_time=0.0,
                timestamp=datetime.now().isoformat()
            )

        except Exception as e:
            logger.error(f"Dependencies validation error: {e}")
            return ValidationResult(
                test_name="dependencies_validation",
                category=ValidationCategory.DEPENDENCIES,
                status=ValidationStatus.ERROR,
                message=f"Validation error: {str(e)}",
                details={"error": str(e)},
                execution_time=0.0,
                timestamp=datetime.now().isoformat()
            )


# ===== VALIDATION RUNNER AND REPORT GENERATOR =====

async def run_atlas_validation(atlas_root_path: str = ".") -> ValidationReport:
    """Run comprehensive A.T.L.A.S validation and generate report"""
    validator = AtlasValidationSystem(atlas_root_path)
    return await validator.run_full_validation()


def print_validation_report(report: ValidationReport):
    """Print a formatted validation report to console"""
    print("\n" + "="*80)
    print(f"🔍 A.T.L.A.S VALIDATION REPORT")
    print("="*80)
    print(f"System: {report.system_name}")
    print(f"Validation Time: {report.validation_timestamp}")
    print(f"Overall Status: {report.overall_status.value.upper()}")
    print(f"Total Tests: {report.total_tests}")
    print(f"✅ Passed: {report.passed_tests}")
    print(f"❌ Failed: {report.failed_tests}")
    print(f"⚠️  Warnings: {report.warning_tests}")
    print(f"⏭️  Skipped: {report.skipped_tests}")
    print(f"💥 Errors: {report.error_tests}")

    print(f"\n📊 SUMMARY:")
    print(f"Success Rate: {report.summary['success_rate']:.1f}%")
    print(f"Critical Issues: {report.summary['critical_issues']}")
    print(f"Components Tested: {report.summary['components_tested']}")
    print(f"Execution Time: {report.summary['total_execution_time']:.2f}s")

    print(f"\n📋 RESULTS BY CATEGORY:")
    for category, results in report.results_by_category.items():
        if results:
            passed = len([r for r in results if r.status == ValidationStatus.PASSED])
            total = len(results)
            print(f"  {category.value}: {passed}/{total} passed")

    if report.recommendations:
        print(f"\n💡 RECOMMENDATIONS:")
        for i, rec in enumerate(report.recommendations, 1):
            print(f"  {i}. {rec}")

    print("\n" + "="*80)


if __name__ == "__main__":
    """Run validation when script is executed directly"""
    import sys

    async def main():
        atlas_path = sys.argv[1] if len(sys.argv) > 1 else "."

        print("🚀 Starting A.T.L.A.S Validation System...")
        report = await run_atlas_validation(atlas_path)

        print_validation_report(report)

        # Exit with appropriate code
        if report.overall_status == ValidationStatus.FAILED:
            sys.exit(1)
        elif report.overall_status == ValidationStatus.WARNING:
            sys.exit(2)
        else:
            sys.exit(0)

    asyncio.run(main())
