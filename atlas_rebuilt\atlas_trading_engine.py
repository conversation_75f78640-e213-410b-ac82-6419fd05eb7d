"""
A.T.L.A.S Trading Engine - Paper Trading and Order Management
Portfolio tracking with lazy Alpaca integration
"""

import asyncio
import logging
from datetime import datetime
from typing import Optional, List, Dict, Any

from config import get_api_config
from models import (
    Order, Position, OrderSide, OrderType, OrderStatus, 
    PortfolioSummary, EngineStatus
)

logger = logging.getLogger(__name__)


class AtlasTradingEngine:
    """
    Trading engine with paper trading and portfolio management
    """
    
    def __init__(self):
        self.config = get_api_config("alpaca")
        self.status = EngineStatus.INITIALIZING
        
        # Alpaca client (lazy loaded)
        self._alpaca_client = None
        self._client_lock = asyncio.Lock()
        
        # Portfolio state
        self.positions = {}
        self.orders = {}
        self.cash_balance = 100000.0  # Starting paper trading balance
        self.total_value = self.cash_balance
        
        # Performance tracking
        self.realized_pnl = 0.0
        self.unrealized_pnl = 0.0
        self.day_start_value = self.cash_balance
        
        logger.info("💼 Trading Engine created - Alpaca client will load on demand")
    
    async def initialize(self):
        """Initialize trading engine"""
        try:
            # Test Alpaca connection if not in paper mode
            if not self.config.get("paper_trading", True):
                await self._ensure_alpaca_client()
                logger.info("✅ Alpaca client connected for live trading")
            else:
                logger.info("✅ Paper trading mode enabled")
            
            # Load existing positions if any
            await self._load_positions()
            
            self.status = EngineStatus.ACTIVE
            logger.info("✅ Trading Engine initialization completed")
            
        except Exception as e:
            logger.error(f"❌ Trading Engine initialization failed: {e}")
            self.status = EngineStatus.DEGRADED
            # Continue with paper trading only
    
    async def _ensure_alpaca_client(self):
        """Ensure Alpaca client is initialized"""
        if self._alpaca_client is None:
            async with self._client_lock:
                if self._alpaca_client is None:
                    try:
                        import alpaca_trade_api as tradeapi
                        self._alpaca_client = tradeapi.REST(
                            self.config["api_key"],
                            self.config["secret_key"],
                            self.config["base_url"],
                            api_version='v2'
                        )
                        
                        # Test connection
                        account = self._alpaca_client.get_account()
                        logger.info(f"✅ Alpaca client initialized - Account: {account.status}")
                        
                    except Exception as e:
                        logger.error(f"❌ Alpaca client initialization failed: {e}")
                        raise
        
        return self._alpaca_client
    
    async def _load_positions(self):
        """Load existing positions from database or Alpaca"""
        try:
            if self.config.get("paper_trading", True):
                # Load from local database (paper trading)
                # This would integrate with the database manager
                logger.info("📊 Loading paper trading positions from database")
            else:
                # Load from Alpaca (live trading)
                client = await self._ensure_alpaca_client()
                alpaca_positions = client.list_positions()
                
                for pos in alpaca_positions:
                    position = Position(
                        symbol=pos.symbol,
                        quantity=float(pos.qty),
                        avg_price=float(pos.avg_cost),
                        current_price=float(pos.market_value) / float(pos.qty) if float(pos.qty) != 0 else 0,
                        unrealized_pnl=float(pos.unrealized_pl),
                        side=OrderSide.BUY if float(pos.qty) > 0 else OrderSide.SELL,
                        timestamp=datetime.now()
                    )
                    self.positions[pos.symbol] = position
                
                logger.info(f"📊 Loaded {len(self.positions)} positions from Alpaca")
                
        except Exception as e:
            logger.error(f"Error loading positions: {e}")
    
    async def place_order(self, symbol: str, quantity: float, side: OrderSide, 
                         order_type: OrderType = OrderType.MARKET, 
                         price: Optional[float] = None,
                         stop_price: Optional[float] = None) -> Order:
        """Place trading order"""
        try:
            # Create order object
            order = Order(
                id=f"atlas_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{symbol}",
                symbol=symbol,
                quantity=quantity,
                side=side,
                type=order_type,
                price=price,
                stop_price=stop_price,
                status=OrderStatus.NEW,
                timestamp=datetime.now()
            )
            
            if self.config.get("paper_trading", True):
                # Paper trading execution
                await self._execute_paper_order(order)
            else:
                # Live trading execution
                await self._execute_live_order(order)
            
            # Store order
            self.orders[order.id] = order
            
            logger.info(f"📋 Order placed: {order.side} {order.quantity} {order.symbol} @ {order.price or 'MARKET'}")
            return order
            
        except Exception as e:
            logger.error(f"Error placing order: {e}")
            raise
    
    async def _execute_paper_order(self, order: Order):
        """Execute order in paper trading mode"""
        try:
            # Simulate market execution
            if order.type == OrderType.MARKET:
                # Get current market price (would integrate with market engine)
                execution_price = order.price or 100.0  # Placeholder
                
                # Execute immediately
                order.status = OrderStatus.FILLED
                order.filled_quantity = order.quantity
                
                # Update position
                await self._update_position(order.symbol, order.quantity, execution_price, order.side)
                
                # Update cash balance
                trade_value = order.quantity * execution_price
                if order.side == OrderSide.BUY:
                    self.cash_balance -= trade_value
                else:
                    self.cash_balance += trade_value
                
                logger.info(f"✅ Paper order executed: {order.symbol} @ ${execution_price:.2f}")
                
            else:
                # Limit/Stop orders would be queued for monitoring
                order.status = OrderStatus.NEW
                logger.info(f"📋 Paper order queued: {order.symbol} {order.type}")
                
        except Exception as e:
            logger.error(f"Paper order execution error: {e}")
            order.status = OrderStatus.REJECTED
            raise
    
    async def _execute_live_order(self, order: Order):
        """Execute order through Alpaca"""
        try:
            client = await self._ensure_alpaca_client()
            
            # Convert to Alpaca order format
            alpaca_order = client.submit_order(
                symbol=order.symbol,
                qty=order.quantity,
                side=order.side.value,
                type=order.type.value,
                time_in_force='day',
                limit_price=order.price if order.type in [OrderType.LIMIT, OrderType.STOP_LIMIT] else None,
                stop_price=order.stop_price if order.type in [OrderType.STOP, OrderType.STOP_LIMIT] else None
            )
            
            # Update order with Alpaca ID
            order.id = alpaca_order.id
            order.status = OrderStatus(alpaca_order.status)
            
            logger.info(f"✅ Live order submitted: {order.symbol} - Alpaca ID: {alpaca_order.id}")
            
        except Exception as e:
            logger.error(f"Live order execution error: {e}")
            order.status = OrderStatus.REJECTED
            raise
    
    async def _update_position(self, symbol: str, quantity: float, price: float, side: OrderSide):
        """Update position after trade execution"""
        try:
            if symbol in self.positions:
                # Update existing position
                position = self.positions[symbol]
                
                if side == OrderSide.BUY:
                    # Adding to position
                    total_cost = (position.quantity * position.avg_price) + (quantity * price)
                    total_quantity = position.quantity + quantity
                    position.avg_price = total_cost / total_quantity if total_quantity != 0 else 0
                    position.quantity = total_quantity
                else:
                    # Reducing position
                    position.quantity -= quantity
                    if position.quantity <= 0:
                        # Position closed
                        self.realized_pnl += (price - position.avg_price) * abs(position.quantity)
                        del self.positions[symbol]
                        return
                
                position.current_price = price
                position.timestamp = datetime.now()
                
            else:
                # New position
                self.positions[symbol] = Position(
                    symbol=symbol,
                    quantity=quantity if side == OrderSide.BUY else -quantity,
                    avg_price=price,
                    current_price=price,
                    unrealized_pnl=0.0,
                    side=side,
                    timestamp=datetime.now()
                )
            
            logger.info(f"📊 Position updated: {symbol} - {self.positions.get(symbol, 'CLOSED')}")
            
        except Exception as e:
            logger.error(f"Position update error: {e}")
    
    async def get_portfolio_summary(self) -> PortfolioSummary:
        """Get current portfolio summary"""
        try:
            # Calculate current values
            positions_value = 0.0
            unrealized_pnl = 0.0
            
            position_list = []
            for position in self.positions.values():
                # Update current prices (would integrate with market engine)
                position.current_price = position.avg_price * 1.01  # Placeholder
                
                position_value = position.quantity * position.current_price
                positions_value += abs(position_value)
                
                position.unrealized_pnl = (position.current_price - position.avg_price) * position.quantity
                unrealized_pnl += position.unrealized_pnl
                
                position_list.append(position)
            
            self.total_value = self.cash_balance + positions_value
            self.unrealized_pnl = unrealized_pnl
            
            # Calculate day change
            day_change = self.total_value - self.day_start_value
            day_change_percent = (day_change / self.day_start_value * 100) if self.day_start_value != 0 else 0
            
            return PortfolioSummary(
                total_value=self.total_value,
                cash_balance=self.cash_balance,
                positions_value=positions_value,
                unrealized_pnl=self.unrealized_pnl,
                realized_pnl=self.realized_pnl,
                day_change=day_change,
                day_change_percent=day_change_percent,
                positions=position_list,
                timestamp=datetime.now()
            )
            
        except Exception as e:
            logger.error(f"Portfolio summary error: {e}")
            raise
    
    async def get_order_status(self, order_id: str) -> Optional[Order]:
        """Get order status"""
        try:
            if order_id in self.orders:
                order = self.orders[order_id]
                
                if not self.config.get("paper_trading", True) and order.status not in [OrderStatus.FILLED, OrderStatus.CANCELED, OrderStatus.REJECTED]:
                    # Update from Alpaca for live orders
                    client = await self._ensure_alpaca_client()
                    alpaca_order = client.get_order(order_id)
                    order.status = OrderStatus(alpaca_order.status)
                    order.filled_quantity = float(alpaca_order.filled_qty)
                
                return order
            
            return None
            
        except Exception as e:
            logger.error(f"Order status error: {e}")
            return None
    
    async def cancel_order(self, order_id: str) -> bool:
        """Cancel pending order"""
        try:
            if order_id in self.orders:
                order = self.orders[order_id]
                
                if self.config.get("paper_trading", True):
                    # Paper trading cancellation
                    if order.status == OrderStatus.NEW:
                        order.status = OrderStatus.CANCELED
                        logger.info(f"✅ Paper order canceled: {order_id}")
                        return True
                else:
                    # Live trading cancellation
                    client = await self._ensure_alpaca_client()
                    client.cancel_order(order_id)
                    order.status = OrderStatus.CANCELED
                    logger.info(f"✅ Live order canceled: {order_id}")
                    return True
            
            return False
            
        except Exception as e:
            logger.error(f"Order cancellation error: {e}")
            return False
    
    async def cleanup(self):
        """Cleanup trading engine resources"""
        try:
            # Save positions to database
            # Cancel any pending orders
            pending_orders = [order for order in self.orders.values() 
                            if order.status == OrderStatus.NEW]
            
            for order in pending_orders:
                await self.cancel_order(order.id)
            
            logger.info("✅ Trading Engine cleanup completed")
            
        except Exception as e:
            logger.error(f"Error during trading engine cleanup: {e}")
