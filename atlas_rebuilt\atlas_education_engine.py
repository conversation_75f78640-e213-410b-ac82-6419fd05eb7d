"""
A.T.L.A.S Education Engine - RAG System with Trading Books
Educational Q&A with lazy ChromaDB loading and trading literature
"""

import asyncio
import logging
from datetime import datetime
from typing import Optional, List, Dict, Any

from config import settings
from models import EducationRequest, AIResponse, EngineStatus

logger = logging.getLogger(__name__)


class AtlasEducationEngine:
    """
    Education engine with RAG system and trading book knowledge base
    """
    
    def __init__(self):
        self.status = EngineStatus.INITIALIZING
        
        # Vector database (lazy loaded)
        self._chroma_client = None
        self._collection = None
        self._embeddings_model = None
        
        # Knowledge base
        self.trading_books = {
            "trading_in_the_zone": "Trading in the Zone by <PERSON>",
            "market_wizards": "Market Wizards by <PERSON>",
            "reminiscences": "Reminiscences of a Stock Operator by <PERSON>",
            "technical_analysis": "Technical Analysis of Financial Markets by <PERSON>",
            "options_strategies": "Options as a Strategic Investment by <PERSON>"
        }
        
        # Educational content cache
        self.content_cache = {}
        self.cache_ttl = 3600  # 1 hour
        
        # Difficulty levels
        self.difficulty_levels = ["beginner", "intermediate", "advanced"]
        
        logger.info("📚 Education Engine created - ChromaDB will load on demand")
    
    async def initialize(self):
        """Initialize education engine with lazy loading"""
        try:
            # Initialize basic educational content
            await self._load_basic_content()
            
            # Test vector database connection (lazy)
            # ChromaDB will be loaded when first needed
            
            self.status = EngineStatus.ACTIVE
            logger.info("✅ Education Engine initialization completed")
            
        except Exception as e:
            logger.error(f"❌ Education Engine initialization failed: {e}")
            self.status = EngineStatus.DEGRADED
            # Continue with basic educational responses
    
    async def _ensure_chroma_client(self):
        """Ensure ChromaDB client is initialized"""
        if self._chroma_client is None:
            try:
                import chromadb
                from chromadb.config import Settings as ChromaSettings
                
                # Initialize ChromaDB client
                self._chroma_client = chromadb.Client(ChromaSettings(
                    chroma_db_impl="duckdb+parquet",
                    persist_directory="./atlas_education_db"
                ))
                
                # Get or create collection
                self._collection = self._chroma_client.get_or_create_collection(
                    name="trading_education",
                    metadata={"description": "Trading education knowledge base"}
                )
                
                logger.info("✅ ChromaDB client initialized")
                
                # Initialize embeddings if collection is empty
                if self._collection.count() == 0:
                    await self._initialize_knowledge_base()
                
            except ImportError:
                logger.warning("⚠️ ChromaDB not available - using fallback responses")
                self._chroma_client = "unavailable"
            except Exception as e:
                logger.error(f"❌ ChromaDB initialization failed: {e}")
                self._chroma_client = "unavailable"
        
        return self._chroma_client if self._chroma_client != "unavailable" else None
    
    async def _load_basic_content(self):
        """Load basic educational content"""
        try:
            # Basic trading concepts
            self.basic_content = {
                "what_is_trading": {
                    "question": "What is trading?",
                    "answer": """Trading is the buying and selling of financial instruments like stocks, bonds, commodities, or currencies with the goal of making a profit. Unlike investing, which typically involves holding assets for longer periods, trading usually involves shorter-term positions to capitalize on price movements.
                    
Key aspects of trading:
• **Timeframes**: Day trading (minutes to hours), swing trading (days to weeks), position trading (weeks to months)
• **Analysis**: Technical analysis (charts, patterns) and fundamental analysis (company/economic data)
• **Risk Management**: Using stop losses, position sizing, and diversification
• **Psychology**: Managing emotions like fear and greed""",
                    "difficulty": "beginner",
                    "topics": ["basics", "introduction"]
                },
                
                "risk_management": {
                    "question": "What is risk management in trading?",
                    "answer": """Risk management is the process of identifying, analyzing, and controlling potential losses in trading. It's arguably the most important aspect of successful trading.
                    
Key risk management principles:
• **Position Sizing**: Never risk more than 1-2% of your account on a single trade
• **Stop Losses**: Predetermined exit points to limit losses
• **Diversification**: Don't put all your money in one stock or sector
• **Risk-Reward Ratio**: Aim for trades where potential profit is at least 2x potential loss
• **Emotional Control**: Stick to your plan and don't let emotions drive decisions""",
                    "difficulty": "beginner",
                    "topics": ["risk", "management", "basics"]
                },
                
                "technical_analysis": {
                    "question": "What is technical analysis?",
                    "answer": """Technical analysis is the study of price charts and trading volume to predict future price movements. It's based on the idea that all relevant information is already reflected in the price.
                    
Key technical analysis tools:
• **Chart Patterns**: Head and shoulders, triangles, flags, support/resistance
• **Indicators**: Moving averages, RSI, MACD, Bollinger Bands
• **Volume Analysis**: Confirming price movements with trading volume
• **Trend Analysis**: Identifying uptrends, downtrends, and sideways markets
• **Candlestick Patterns**: Reading price action through candlestick formations""",
                    "difficulty": "intermediate",
                    "topics": ["technical", "analysis", "charts"]
                }
            }
            
            logger.info("📖 Basic educational content loaded")
            
        except Exception as e:
            logger.error(f"Error loading basic content: {e}")
    
    async def _initialize_knowledge_base(self):
        """Initialize knowledge base with trading book content"""
        try:
            if not self._collection:
                return
            
            # Sample trading book excerpts (in production, these would be full book contents)
            book_excerpts = [
                {
                    "id": "tz_001",
                    "text": "The hard, cold reality is that every trade has an uncertain outcome. Unless you learn to completely accept the possibility of being wrong, you will try to avoid the possibility of being wrong. This will absolutely prevent you from learning how to be objective, which will keep you from recognizing what the market is telling you about the direction it wants to move.",
                    "source": "Trading in the Zone",
                    "topic": "psychology",
                    "difficulty": "intermediate"
                },
                {
                    "id": "mw_001", 
                    "text": "The key to trading success is emotional discipline. If intelligence were the key, there would be a lot more people making money trading. I know this will sound like a cliché, but the single most important reason that people lose money in the financial markets is that they don't cut their losses short.",
                    "source": "Market Wizards",
                    "topic": "discipline",
                    "difficulty": "intermediate"
                },
                {
                    "id": "ta_001",
                    "text": "The trend is your friend until it ends. One of the most important concepts in technical analysis is the identification and following of market trends. Markets tend to move in trends, and these trends persist for extended periods.",
                    "source": "Technical Analysis of Financial Markets",
                    "topic": "trends",
                    "difficulty": "beginner"
                }
            ]
            
            # Add to ChromaDB
            for excerpt in book_excerpts:
                self._collection.add(
                    documents=[excerpt["text"]],
                    metadatas=[{
                        "source": excerpt["source"],
                        "topic": excerpt["topic"],
                        "difficulty": excerpt["difficulty"]
                    }],
                    ids=[excerpt["id"]]
                )
            
            logger.info(f"📚 Knowledge base initialized with {len(book_excerpts)} excerpts")
            
        except Exception as e:
            logger.error(f"Knowledge base initialization error: {e}")
    
    async def process_query(self, request: EducationRequest) -> AIResponse:
        """Process educational query with RAG system"""
        try:
            question = request.question.lower()
            
            # Check cache first
            cache_key = f"{question}_{request.difficulty_level}"
            if cache_key in self.content_cache:
                cached_time = self.content_cache[cache_key]["timestamp"]
                if (datetime.now() - cached_time).seconds < self.cache_ttl:
                    cached_response = self.content_cache[cache_key]["response"]
                    return AIResponse(
                        response=cached_response,
                        type="education",
                        confidence=0.8,
                        context={"source": "cache"}
                    )
            
            # Try RAG system first
            rag_response = await self._query_knowledge_base(request)
            if rag_response:
                # Cache the response
                self.content_cache[cache_key] = {
                    "response": rag_response,
                    "timestamp": datetime.now()
                }
                
                return AIResponse(
                    response=rag_response,
                    type="education",
                    confidence=0.9,
                    context={"source": "knowledge_base"}
                )
            
            # Fallback to basic content
            basic_response = await self._query_basic_content(request)
            if basic_response:
                return AIResponse(
                    response=basic_response,
                    type="education",
                    confidence=0.7,
                    context={"source": "basic_content"}
                )
            
            # Final fallback
            return await self._generate_fallback_response(request)
            
        except Exception as e:
            logger.error(f"Education query processing error: {e}")
            return AIResponse(
                response="I encountered an error processing your educational query. Please try rephrasing your question.",
                type="error",
                confidence=0.0
            )
    
    async def _query_knowledge_base(self, request: EducationRequest) -> Optional[str]:
        """Query ChromaDB knowledge base"""
        try:
            client = await self._ensure_chroma_client()
            if not client or not self._collection:
                return None
            
            # Search for relevant content
            results = self._collection.query(
                query_texts=[request.question],
                n_results=3,
                where={"difficulty": request.difficulty_level} if request.difficulty_level != "beginner" else None
            )
            
            if results["documents"] and len(results["documents"][0]) > 0:
                # Combine relevant excerpts
                relevant_content = []
                for i, doc in enumerate(results["documents"][0]):
                    source = results["metadatas"][0][i]["source"]
                    relevant_content.append(f"From '{source}':\n{doc}")
                
                # Generate comprehensive response
                combined_content = "\n\n".join(relevant_content)
                
                response = f"""Based on trading literature, here's what I found about your question:

{combined_content}

**Key Takeaways:**
• This information comes from respected trading books and experts
• Consider your experience level when applying these concepts
• Always practice risk management in your trading

Would you like me to explain any of these concepts in more detail?"""
                
                return response
            
        except Exception as e:
            logger.error(f"Knowledge base query error: {e}")
        
        return None
    
    async def _query_basic_content(self, request: EducationRequest) -> Optional[str]:
        """Query basic educational content"""
        try:
            question_lower = request.question.lower()
            
            # Simple keyword matching
            for content_id, content in self.basic_content.items():
                content_text = content["answer"].lower()
                question_text = content["question"].lower()
                
                # Check if question matches or contains key terms
                if any(keyword in question_lower for keyword in content["topics"]):
                    return f"""**{content['question']}**

{content['answer']}

**Difficulty Level:** {content['difficulty'].title()}

This is fundamental trading knowledge. Would you like me to elaborate on any specific aspect?"""
            
        except Exception as e:
            logger.error(f"Basic content query error: {e}")
        
        return None
    
    async def _generate_fallback_response(self, request: EducationRequest) -> AIResponse:
        """Generate fallback educational response"""
        try:
            fallback_responses = {
                "beginner": """I'd be happy to help with your trading education question! While I'm still loading my complete knowledge base, here are some fundamental concepts:

**Getting Started with Trading:**
• Start with paper trading to practice without risk
• Learn basic chart reading and technical indicators
• Understand risk management principles
• Study successful traders and their strategies

**Recommended Learning Path:**
1. Master the basics of market mechanics
2. Learn technical analysis fundamentals
3. Develop a trading plan and strategy
4. Practice with small positions
5. Continuously educate yourself

Would you like me to elaborate on any of these areas?""",
                
                "intermediate": """For intermediate trading concepts, I recommend focusing on:

**Advanced Technical Analysis:**
• Multiple timeframe analysis
• Advanced chart patterns
• Volume analysis techniques
• Market structure understanding

**Risk Management:**
• Position sizing strategies
• Portfolio correlation analysis
• Drawdown management
• Performance metrics

**Trading Psychology:**
• Emotional discipline
• Cognitive biases in trading
• Developing consistent routines

What specific area would you like to explore further?""",
                
                "advanced": """For advanced trading topics, consider these areas:

**Quantitative Analysis:**
• Statistical arbitrage
• Algorithmic trading concepts
• Options strategies
• Portfolio optimization

**Market Microstructure:**
• Order flow analysis
• Market making concepts
• Liquidity considerations

**Professional Trading:**
• Risk management systems
• Performance attribution
• Regulatory considerations

Which advanced topic interests you most?"""
            }
            
            response_text = fallback_responses.get(
                request.difficulty_level, 
                fallback_responses["beginner"]
            )
            
            return AIResponse(
                response=response_text,
                type="education",
                confidence=0.6,
                context={"source": "fallback", "difficulty": request.difficulty_level}
            )
            
        except Exception as e:
            logger.error(f"Fallback response generation error: {e}")
            return AIResponse(
                response="I'm currently setting up my educational systems. Please try your question again in a moment.",
                type="system_status",
                confidence=0.3
            )
    
    async def get_available_topics(self) -> List[str]:
        """Get list of available educational topics"""
        try:
            topics = set()
            
            # Add basic content topics
            for content in self.basic_content.values():
                topics.update(content["topics"])
            
            # Add knowledge base topics if available
            client = await self._ensure_chroma_client()
            if client and self._collection:
                # This would query unique topics from the knowledge base
                topics.update(["psychology", "discipline", "trends", "risk_management"])
            
            return sorted(list(topics))
            
        except Exception as e:
            logger.error(f"Error getting available topics: {e}")
            return ["basics", "risk_management", "technical_analysis"]
    
    async def cleanup(self):
        """Cleanup education engine resources"""
        try:
            # Clear cache
            self.content_cache.clear()
            
            # Close ChromaDB connection if needed
            if self._chroma_client and self._chroma_client != "unavailable":
                # ChromaDB client doesn't need explicit closing
                pass
            
            logger.info("✅ Education Engine cleanup completed")
            
        except Exception as e:
            logger.error(f"Error during education engine cleanup: {e}")
