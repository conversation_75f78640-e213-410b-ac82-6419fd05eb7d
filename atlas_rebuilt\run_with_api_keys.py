#!/usr/bin/env python3
"""
Run A.T.L.A.S with REAL API keys - NO MORE VALIDATION MODE!
"""

import os
import sys

def setup_real_api_keys():
    """Set up the real API keys from your memories"""
    print("🔥 ACTIVATING A.T.L.A.S WITH REAL API KEYS!")
    print("=" * 50)
    
    # Set the REAL API keys
    api_keys = {
        "ALPACA_API_KEY": "PKI0KNC8HXZURYRA4OMC",
        "ALPACA_SECRET_KEY": "your_alpaca_secret_here",  # You need to provide this
        "FMP_API_KEY": "K63wnAbUDHNPICjzeq3JcEG1vi8Q2oz7",
        "OPENAI_API_KEY": "********************************************************************************************************************************************************************",
        "PREDICTO_API_KEY": "VZ19mf7DvVovUKW0E7PTYmzrJkQjkC5N5fMcWwOsglWPFzhSQPU8m77cb3d3k760"
    }
    
    # DISABLE validation mode
    os.environ["VALIDATION_MODE"] = "false"
    
    # Set all the API keys
    for key, value in api_keys.items():
        os.environ[key] = value
        print(f"✅ {key}: {value[:10]}...{value[-4:]}")
    
    print("\n🚀 PRODUCTION MODE ACTIVATED!")
    print("📊 Real market data: ENABLED")
    print("🧠 Full AI analysis: ENABLED") 
    print("💼 Live trading: ENABLED")
    print("📈 Predicto predictions: ENABLED")
    
    return True

def run_atlas_production():
    """Run A.T.L.A.S in full production mode"""
    try:
        print("\n🌐 Starting A.T.L.A.S Production Server...")
        print("🔗 URL: http://localhost:8080")
        print("📚 API Docs: http://localhost:8080/docs")
        print("💬 Chat Interface: http://localhost:8080")
        
        # Import and run the server
        import uvicorn
        from atlas_server_fixed import app
        
        uvicorn.run(
            app,
            host="0.0.0.0", 
            port=8080,
            log_level="info",
            reload=False
        )
        
    except Exception as e:
        print(f"❌ Server failed to start: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("🔥 A.T.L.A.S PRODUCTION MODE STARTUP")
    print("⚠️  WARNING: REAL API KEYS - REAL TRADING ENABLED!")
    print()
    
    # Set up API keys
    if setup_real_api_keys():
        print("\n🚀 Starting server with FULL CAPABILITIES...")
        run_atlas_production()
    else:
        print("❌ Failed to set up API keys")
        sys.exit(1)
