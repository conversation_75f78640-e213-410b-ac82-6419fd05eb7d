#!/usr/bin/env python3
"""
Run A.T.L.A.S with REAL API keys - NO MORE VALIDATION MODE!
"""

import os
import sys

def setup_real_api_keys():
    """Set up the real API keys from your memories"""
    print("🔥 ACTIVATING A.T.L.A.S WITH REAL API KEYS!")
    print("=" * 50)
    
    # Set the REAL API keys
    api_keys = {
        "ALPACA_API_KEY": "PKI0KNC8HXZURYRA4OMC",
        "ALPACA_SECRET_KEY": os.environ.get("ALPACA_SECRET_KEY", ""),  # Get from environment
        "FMP_API_KEY": "K63wnAbUDHNPICjzeq3JcEG1vi8Q2oz7",
        "OPENAI_API_KEY": "********************************************************************************************************************************************************************",
        "PREDICTO_API_KEY": "VZ19mf7DvVovUKW0E7PTYmzrJkQjkC5N5fMcWwOsglWPFzhSQPU8m77cb3d3k760"
    }

    # Check if Alpaca secret key is provided
    if not api_keys["ALPACA_SECRET_KEY"] or api_keys["ALPACA_SECRET_KEY"] == "your_actual_secret_key":
        print("❌ ALPACA_SECRET_KEY not provided!")
        print("💡 You need to set your real Alpaca secret key:")
        print("   export ALPACA_SECRET_KEY='your_real_secret_key'")
        print("   OR edit the script to include it")
        return False
    
    # DISABLE validation mode
    os.environ["VALIDATION_MODE"] = "false"
    
    # Set all the API keys
    for key, value in api_keys.items():
        os.environ[key] = value
        print(f"✅ {key}: {value[:10]}...{value[-4:]}")
    
    print("\n🚀 PRODUCTION MODE ACTIVATED!")
    print("📊 Real market data: ENABLED")
    print("🧠 Full AI analysis: ENABLED") 
    print("💼 Live trading: ENABLED")
    print("📈 Predicto predictions: ENABLED")
    
    return True

def run_atlas_production():
    """Run A.T.L.A.S in full production mode"""
    try:
        print("\n🌐 Starting A.T.L.A.S Production Server...")
        print("🔗 URL: http://localhost:8080")
        print("📚 API Docs: http://localhost:8080/docs")
        print("💬 Chat Interface: http://localhost:8080")
        
        # Import and run the server
        import uvicorn
        from atlas_server_fixed import app
        
        # Try different ports if 8080 is busy
        ports_to_try = [8080, 8081, 8082, 8083]

        for port in ports_to_try:
            try:
                print(f"🔄 Trying port {port}...")
                uvicorn.run(
                    app,
                    host="0.0.0.0",
                    port=port,
                    log_level="info",
                    reload=False
                )
                break
            except OSError as e:
                if "10048" in str(e):  # Port already in use
                    print(f"⚠️ Port {port} is busy, trying next...")
                    continue
                else:
                    raise e
        
    except Exception as e:
        print(f"❌ Server failed to start: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("🔥 A.T.L.A.S PRODUCTION MODE STARTUP")
    print("⚠️  WARNING: REAL API KEYS - REAL TRADING ENABLED!")
    print()
    
    # Set up API keys
    if setup_real_api_keys():
        print("\n🚀 Starting server with FULL CAPABILITIES...")
        run_atlas_production()
    else:
        print("❌ Failed to set up API keys")
        sys.exit(1)
