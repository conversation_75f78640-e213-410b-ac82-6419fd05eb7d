#!/usr/bin/env python3
"""
Test server startup in validation mode
"""

import os
import sys
import asyncio
import logging

# Set validation mode before importing anything
os.environ['VALIDATION_MODE'] = 'true'

async def test_server_startup():
    """Test that the server can start in validation mode"""
    try:
        print("🚀 Testing A.T.L.A.S Server Startup in Validation Mode...")
        
        # Import after setting environment variable
        from config import settings
        print(f"✅ Config loaded - Validation Mode: {settings.VALIDATION_MODE}")
        
        # Test orchestrator creation
        from atlas_orchestrator import AtlasOrchestrator
        orchestrator = AtlasOrchestrator()
        print("✅ Orchestrator created successfully")
        
        # Test component initialization
        print("🔧 Testing component initialization...")
        
        try:
            ai_engine = await orchestrator._ensure_ai_engine()
            print(f"  AI Engine: {'✅ OPERATIONAL' if ai_engine else '⚠️ DEGRADED'}")
        except Exception as e:
            print(f"  AI Engine: ❌ FAILED - {e}")
        
        try:
            trading_engine = await orchestrator._ensure_trading_engine()
            print(f"  Trading Engine: {'✅ OPERATIONAL' if trading_engine else '⚠️ DEGRADED'}")
        except Exception as e:
            print(f"  Trading Engine: ❌ FAILED - {e}")
        
        try:
            market_engine = await orchestrator._ensure_market_engine()
            print(f"  Market Engine: {'✅ OPERATIONAL' if market_engine else '⚠️ DEGRADED'}")
        except Exception as e:
            print(f"  Market Engine: ❌ FAILED - {e}")
        
        try:
            risk_engine = await orchestrator._ensure_risk_engine()
            print(f"  Risk Engine: {'✅ OPERATIONAL' if risk_engine else '⚠️ DEGRADED'}")
        except Exception as e:
            print(f"  Risk Engine: ❌ FAILED - {e}")
        
        try:
            education_engine = await orchestrator._ensure_education_engine()
            print(f"  Education Engine: {'✅ OPERATIONAL' if education_engine else '⚠️ DEGRADED'}")
        except Exception as e:
            print(f"  Education Engine: ❌ FAILED - {e}")
        
        # Test message processing
        print("\n💬 Testing message processing...")
        try:
            response = await orchestrator.process_message(
                "What is RSI?", 
                "test-session"
            )
            if response and hasattr(response, 'response'):
                print("✅ Message processing working")
                print(f"   Response length: {len(response.response)} characters")
            else:
                print("⚠️ Message processing degraded")
        except Exception as e:
            print(f"❌ Message processing failed: {e}")
        
        print("\n🎉 Server startup test completed successfully!")
        print("✅ A.T.L.A.S is ready to run in validation mode")
        
        return True
        
    except Exception as e:
        print(f"❌ Server startup test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(test_server_startup())
    sys.exit(0 if success else 1)
