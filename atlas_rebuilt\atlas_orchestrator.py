"""
A.T.L.A.S Orchestrator - Lazy-Loading System Coordinator
Unified coordinator with on-demand component initialization
"""

import asyncio
import logging
from datetime import datetime
from typing import Optional, Dict, Any, Callable

from config import settings
from models import (
    AIResponse, EngineStatus, UserProfile, ChatRequest
)

logger = logging.getLogger(__name__)


class AtlasOrchestrator:
    """
    Master orchestrator with lazy component loading and graceful degradation
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # Component references (initialized lazily)
        self._ai_engine = None
        self._market_engine = None
        self._trading_engine = None
        self._risk_engine = None
        self._education_engine = None
        self._database_manager = None
        
        # Component initialization status
        self._component_status = {
            "ai_engine": EngineStatus.INITIALIZING,
            "market_engine": EngineStatus.INITIALIZING,
            "trading_engine": EngineStatus.INITIALIZING,
            "risk_engine": EngineStatus.INITIALIZING,
            "education_engine": EngineStatus.INITIALIZING,
            "database_manager": EngineStatus.INITIALIZING
        }
        
        # User profile and session management
        self.user_profile = UserProfile()
        self.current_session_id = None
        
        # Initialization locks to prevent concurrent loading
        self._init_locks = {
            "ai_engine": asyncio.Lock(),
            "market_engine": asyncio.Lock(),
            "trading_engine": asyncio.Lock(),
            "risk_engine": asyncio.Lock(),
            "education_engine": asyncio.Lock(),
            "database_manager": asyncio.Lock()
        }
        
        self.logger.info("🎯 AtlasOrchestrator created - components will load on demand")
    
    async def initialize_with_progress(self, progress_callback: Callable):
        """Initialize all components with progress tracking"""
        try:
            self.logger.info("🔧 Starting comprehensive system initialization...")
            
            # Initialize database manager first
            progress_callback("database_manager", 0.1, EngineStatus.INITIALIZING, "Starting database initialization")
            await self._ensure_database_manager()
            progress_callback("database_manager", 1.0, self._component_status["database_manager"], "Database manager ready")
            
            # Initialize other components in parallel
            tasks = []
            
            # AI Engine
            async def init_ai():
                progress_callback("ai_engine", 0.1, EngineStatus.INITIALIZING, "Loading AI models")
                await self._ensure_ai_engine()
                progress_callback("ai_engine", 1.0, self._component_status["ai_engine"], "AI engine ready")
            
            # Market Engine
            async def init_market():
                progress_callback("market_engine", 0.1, EngineStatus.INITIALIZING, "Connecting to market data")
                await self._ensure_market_engine()
                progress_callback("market_engine", 1.0, self._component_status["market_engine"], "Market engine ready")
            
            # Trading Engine
            async def init_trading():
                progress_callback("trading_engine", 0.1, EngineStatus.INITIALIZING, "Setting up trading systems")
                await self._ensure_trading_engine()
                progress_callback("trading_engine", 1.0, self._component_status["trading_engine"], "Trading engine ready")
            
            # Risk Engine
            async def init_risk():
                progress_callback("risk_engine", 0.1, EngineStatus.INITIALIZING, "Initializing risk management")
                await self._ensure_risk_engine()
                progress_callback("risk_engine", 1.0, self._component_status["risk_engine"], "Risk engine ready")
            
            # Education Engine
            async def init_education():
                progress_callback("education_engine", 0.1, EngineStatus.INITIALIZING, "Loading educational content")
                await self._ensure_education_engine()
                progress_callback("education_engine", 1.0, self._component_status["education_engine"], "Education engine ready")
            
            # Run initialization tasks
            tasks = [init_ai(), init_market(), init_trading(), init_risk(), init_education()]
            await asyncio.gather(*tasks, return_exceptions=True)
            
            self.logger.info("✅ System initialization completed")
            
        except Exception as e:
            self.logger.error(f"❌ System initialization failed: {e}")
            raise
    
    async def _ensure_database_manager(self):
        """Ensure database manager is initialized"""
        if self._database_manager is None:
            async with self._init_locks["database_manager"]:
                if self._database_manager is None:
                    try:
                        from atlas_database_manager import AtlasDatabaseManager
                        self._database_manager = AtlasDatabaseManager()
                        await self._database_manager.initialize()
                        self._component_status["database_manager"] = EngineStatus.ACTIVE
                        self.logger.info("✅ Database manager initialized")
                    except Exception as e:
                        self.logger.error(f"❌ Database manager initialization failed: {e}")
                        self._component_status["database_manager"] = EngineStatus.FAILED
                        raise
        return self._database_manager
    
    async def _ensure_ai_engine(self):
        """Ensure AI engine is initialized"""
        if self._ai_engine is None:
            async with self._init_locks["ai_engine"]:
                if self._ai_engine is None:
                    try:
                        from atlas_ai_engine import AtlasAIEngine
                        self._ai_engine = AtlasAIEngine()
                        await self._ai_engine.initialize()
                        self._component_status["ai_engine"] = EngineStatus.ACTIVE
                        self.logger.info("✅ AI engine initialized")
                    except Exception as e:
                        self.logger.error(f"❌ AI engine initialization failed: {e}")
                        self._component_status["ai_engine"] = EngineStatus.FAILED
                        # Continue without AI engine - use fallback responses
        return self._ai_engine
    
    async def _ensure_market_engine(self):
        """Ensure market engine is initialized"""
        if self._market_engine is None:
            async with self._init_locks["market_engine"]:
                if self._market_engine is None:
                    try:
                        from atlas_market_engine import AtlasMarketEngine
                        self._market_engine = AtlasMarketEngine()
                        await self._market_engine.initialize()
                        self._component_status["market_engine"] = EngineStatus.ACTIVE
                        self.logger.info("✅ Market engine initialized")
                    except Exception as e:
                        self.logger.error(f"❌ Market engine initialization failed: {e}")
                        self._component_status["market_engine"] = EngineStatus.FAILED
        return self._market_engine
    
    async def _ensure_trading_engine(self):
        """Ensure trading engine is initialized"""
        if self._trading_engine is None:
            async with self._init_locks["trading_engine"]:
                if self._trading_engine is None:
                    try:
                        from atlas_trading_engine import AtlasTradingEngine
                        self._trading_engine = AtlasTradingEngine()
                        await self._trading_engine.initialize()
                        self._component_status["trading_engine"] = EngineStatus.ACTIVE
                        self.logger.info("✅ Trading engine initialized")
                    except Exception as e:
                        self.logger.error(f"❌ Trading engine initialization failed: {e}")
                        self._component_status["trading_engine"] = EngineStatus.FAILED
        return self._trading_engine
    
    async def _ensure_risk_engine(self):
        """Ensure risk engine is initialized"""
        if self._risk_engine is None:
            async with self._init_locks["risk_engine"]:
                if self._risk_engine is None:
                    try:
                        from atlas_risk_engine import AtlasRiskEngine
                        self._risk_engine = AtlasRiskEngine()
                        await self._risk_engine.initialize()
                        self._component_status["risk_engine"] = EngineStatus.ACTIVE
                        self.logger.info("✅ Risk engine initialized")
                    except Exception as e:
                        self.logger.error(f"❌ Risk engine initialization failed: {e}")
                        self._component_status["risk_engine"] = EngineStatus.FAILED
        return self._risk_engine
    
    async def _ensure_education_engine(self):
        """Ensure education engine is initialized"""
        if self._education_engine is None:
            async with self._init_locks["education_engine"]:
                if self._education_engine is None:
                    try:
                        from atlas_education_engine import AtlasEducationEngine
                        self._education_engine = AtlasEducationEngine()
                        await self._education_engine.initialize()
                        self._component_status["education_engine"] = EngineStatus.ACTIVE
                        self.logger.info("✅ Education engine initialized")
                    except Exception as e:
                        self.logger.error(f"❌ Education engine initialization failed: {e}")
                        self._component_status["education_engine"] = EngineStatus.FAILED
        return self._education_engine
    
    # Property accessors with lazy loading
    @property
    async def ai_engine(self):
        return await self._ensure_ai_engine()
    
    @property
    async def market_engine(self):
        return await self._ensure_market_engine()
    
    @property
    async def trading_engine(self):
        return await self._ensure_trading_engine()
    
    @property
    async def risk_engine(self):
        return await self._ensure_risk_engine()
    
    @property
    async def education_engine(self):
        return await self._ensure_education_engine()
    
    @property
    async def database_manager(self):
        return await self._ensure_database_manager()
    
    async def process_message(self, message: str, session_id: Optional[str] = None) -> AIResponse:
        """Process user message with graceful degradation"""
        try:
            # Ensure AI engine is available
            ai_engine = await self._ensure_ai_engine()
            
            if ai_engine and self._component_status["ai_engine"] == EngineStatus.ACTIVE:
                # Full AI processing
                return await ai_engine.process_message(message, session_id, self)
            else:
                # Fallback response when AI engine is not available
                return AIResponse(
                    response=f"I received your message: '{message}'. However, my AI systems are currently initializing. Please try again in a moment for full analysis capabilities.",
                    type="fallback",
                    confidence=0.3,
                    context={"fallback_reason": "AI engine not available"}
                )
                
        except Exception as e:
            self.logger.error(f"Error processing message: {e}")
            return AIResponse(
                response="I encountered an error processing your message. Please try again.",
                type="error",
                confidence=0.0,
                context={"error": str(e)}
            )
    
    async def cleanup(self):
        """Cleanup all components"""
        self.logger.info("🧹 Cleaning up A.T.L.A.S components...")
        
        cleanup_tasks = []
        
        if self._market_engine:
            cleanup_tasks.append(self._market_engine.cleanup())
        if self._trading_engine:
            cleanup_tasks.append(self._trading_engine.cleanup())
        if self._ai_engine:
            cleanup_tasks.append(self._ai_engine.cleanup())
        if self._risk_engine:
            cleanup_tasks.append(self._risk_engine.cleanup())
        if self._education_engine:
            cleanup_tasks.append(self._education_engine.cleanup())
        if self._database_manager:
            cleanup_tasks.append(self._database_manager.cleanup())
        
        if cleanup_tasks:
            await asyncio.gather(*cleanup_tasks, return_exceptions=True)
        
        self.logger.info("✅ A.T.L.A.S cleanup completed")
    
    def get_component_status(self) -> Dict[str, EngineStatus]:
        """Get current status of all components"""
        return self._component_status.copy()
