{"system_name": "A.T.L.A.S AI Trading System", "validation_timestamp": "2025-06-27T17:13:35.698041", "total_tests": 7, "passed_tests": 1, "failed_tests": 4, "warning_tests": 1, "skipped_tests": 0, "error_tests": 1, "overall_status": "failed", "summary": {"total_execution_time": 0.89264, "success_rate": 14.285714285714285, "critical_issues": 5, "warnings": 1, "components_tested": 0, "categories_tested": 7}, "recommendations": ["Address 4 critical failures before deployment", "Review 1 warnings for potential improvements", "Initialize missing components: orchestrator, ai_engine, trading_engine, market_engine, education_engine, risk_engine, server"], "results_by_category": {"feature_coverage": [{"test_name": "feature_coverage_validation", "category": "feature_coverage", "status": "warning", "message": "Undocumented features: 142 features", "details": {"documented_features": [], "implemented_features": ["init_ai", "_identify_risk_factors", "_meets_strength_criteria", "get_market_context", "_calculate_5_star_rating", "_analyze_basic_sentiment", "_analyze_with_distilbert", "_process_education_query", "get_quote", "get_pending_trades", "suggest_hedging_strategies", "_strength_to_numeric", "lifespan", "generate_trade_confirmation_message", "_ensure_predicto_session", "_basic_ttm_fallback", "_ensure_market_engine", "initialize", "_initialize_agents", "_analyze_intent", "process_message", "_initialize_ml_models", "_update_position", "_execute_confirmed_trade", "health_check", "_calculate_technical_stop_loss", "root", "_google_search", "_ensure_education_engine", "_query_basic_content", "_generate_action_plan", "/api/v1/risk-assessment", "ai_engine", "get_order_status", "init_risk", "get_portfolio_risk_analysis", "get_portfolio_summary", "_duckduckgo_search", "update_progress_callback", "_get_historical_data", "get_component_status", "_calculate_confidence", "get_available_topics", "/api/v1/portfolio/optimization", "scan_market", "/api/v1/trading/pending-trades", "_load_risk_parameters", "/api/v1/trading/confirm-trade", "/api/v1/market/news/{symbol}", "_analyze_technical_patterns", "/api/v1/market/context/{symbol}", "_generate_fallback_response", "database_manager", "analyze_portfolio_risk", "cleanup", "prepare_trade_for_confirmation", "_execute_live_order", "__init__", "_calculate_volatility_stop_loss", "market_engine", "education_engine", "place_order", "_calculate_risk_score", "enable_auto_reinvestment", "_analyze_execution_timing", "/api/v1/predicto/forecast/{symbol}", "initialize_system", "_analyze_search_results", "_calculate_ttm_squeeze", "_initialize_knowledge_base", "/api/v1/education", "_ensure_ai_engine", "/api/v1/portfolio/hedging/{symbol}", "_reality_check_goals", "_fallback_response", "chat_endpoint", "_ensure_alpaca_client", "init_market", "_perform_web_search", "prepare_trade_confirmation", "_ensure_risk_engine", "_process_trading_analysis", "_ensure_chroma_client", "_ensure_trading_engine", "trading_engine", "search_market_context", "check_circuit_breakers", "get_predicto_forecast", "_calculate_support_stop_loss", "/api/v1/portfolio/auto-reinvestment", "confirm_trade_execution", "market_scan", "_build_search_query", "_generate_recommendations", "_ensure_database_manager", "init_education", "_generate_news_summary", "_test_connections", "_generate_chain_of_thought", "get_portfolio", "_get_comprehensive_trading_content", "_query_knowledge_base", "_initialize_risk_monitoring", "_analyze_market_sentiment", "process_query", "/api/v1/health", "/api/v1/initialization/status", "/api/v1/quote/{symbol}", "risk_engine", "_extract_symbol_from_message", "analyze", "_calculate_market_relevance", "_analyze_risk_patterns", "init_trading", "_ensure_openai_client", "/", "_calculate_position_size", "_parse_trading_goals", "_process_general_chat", "_get_volatility_factor", "risk_assessment", "/api/v1/trading/prepare-trade", "/api/v1/chat", "get_initialization_status", "get_hedging_strategies", "_get_fmp_quote", "_generate_consensus", "cancel_order", "assess_risk", "/api/v1/scan", "_initialize_web_search_config", "education_query", "get_market_news", "/api/v1/portfolio/risk-analysis", "get_portfolio_optimization", "_load_positions", "initialize_with_progress", "_ensure_fmp_session", "/api/v1/portfolio", "_load_basic_content", "_execute_paper_order", "_calculate_ai_stop_loss"], "missing_implementations": [], "undocumented_features": ["init_ai", "_identify_risk_factors", "_meets_strength_criteria", "get_market_context", "_calculate_5_star_rating", "_analyze_basic_sentiment", "_analyze_with_distilbert", "_process_education_query", "get_quote", "get_pending_trades", "suggest_hedging_strategies", "_strength_to_numeric", "lifespan", "generate_trade_confirmation_message", "_ensure_predicto_session", "_basic_ttm_fallback", "_ensure_market_engine", "initialize", "_initialize_agents", "_analyze_intent", "process_message", "_initialize_ml_models", "_update_position", "_execute_confirmed_trade", "health_check", "_calculate_technical_stop_loss", "root", "_google_search", "_ensure_education_engine", "_query_basic_content", "_generate_action_plan", "/api/v1/risk-assessment", "ai_engine", "get_order_status", "init_risk", "get_portfolio_risk_analysis", "get_portfolio_summary", "_duckduckgo_search", "update_progress_callback", "_get_historical_data", "get_component_status", "_calculate_confidence", "get_available_topics", "/api/v1/portfolio/optimization", "scan_market", "/api/v1/trading/pending-trades", "_load_risk_parameters", "/api/v1/trading/confirm-trade", "/api/v1/market/news/{symbol}", "_analyze_technical_patterns", "/api/v1/market/context/{symbol}", "_generate_fallback_response", "database_manager", "analyze_portfolio_risk", "cleanup", "prepare_trade_for_confirmation", "_execute_live_order", "__init__", "_calculate_volatility_stop_loss", "market_engine", "education_engine", "place_order", "_calculate_risk_score", "enable_auto_reinvestment", "_analyze_execution_timing", "/api/v1/predicto/forecast/{symbol}", "initialize_system", "_analyze_search_results", "_calculate_ttm_squeeze", "_initialize_knowledge_base", "/api/v1/education", "_ensure_ai_engine", "/api/v1/portfolio/hedging/{symbol}", "_reality_check_goals", "_fallback_response", "chat_endpoint", "_ensure_alpaca_client", "init_market", "_perform_web_search", "prepare_trade_confirmation", "_ensure_risk_engine", "_process_trading_analysis", "_ensure_chroma_client", "_ensure_trading_engine", "trading_engine", "search_market_context", "check_circuit_breakers", "get_predicto_forecast", "_calculate_support_stop_loss", "/api/v1/portfolio/auto-reinvestment", "confirm_trade_execution", "market_scan", "_build_search_query", "_generate_recommendations", "_ensure_database_manager", "init_education", "_generate_news_summary", "_test_connections", "_generate_chain_of_thought", "get_portfolio", "_get_comprehensive_trading_content", "_query_knowledge_base", "_initialize_risk_monitoring", "_analyze_market_sentiment", "process_query", "/api/v1/health", "/api/v1/initialization/status", "/api/v1/quote/{symbol}", "risk_engine", "_extract_symbol_from_message", "analyze", "_calculate_market_relevance", "_analyze_risk_patterns", "init_trading", "_ensure_openai_client", "/", "_calculate_position_size", "_parse_trading_goals", "_process_general_chat", "_get_volatility_factor", "risk_assessment", "/api/v1/trading/prepare-trade", "/api/v1/chat", "get_initialization_status", "get_hedging_strategies", "_get_fmp_quote", "_generate_consensus", "cancel_order", "assess_risk", "/api/v1/scan", "_initialize_web_search_config", "education_query", "get_market_news", "/api/v1/portfolio/risk-analysis", "get_portfolio_optimization", "_load_positions", "initialize_with_progress", "_ensure_fmp_session", "/api/v1/portfolio", "_load_basic_content", "_execute_paper_order", "_calculate_ai_stop_loss"], "coverage_percentage": 100}, "execution_time": 0.002413, "timestamp": "2025-06-27T17:13:35.063676"}], "api_endpoints": [{"test_name": "api_endpoints_validation", "category": "api_endpoints", "status": "failed", "message": "Missing endpoint implementations: 16", "details": {"documented_endpoints": ["/api/v1/chat", "/api/v1/health", "/api/v1/initialization/status", "/api/v1/quote/{symbol}", "/api/v1/scan?min_strength={level}", "/api/v1/predicto/forecast/{symbol}?days={1-30}", "/api/v1/portfolio", "/api/v1/risk-assessment", "/api/v1/education", "\n\n### **4. Access the System**\n- **Web Interface**: http://localhost:8080\n- **API Documentation**: http://localhost:8080/docs\n- **Health Check**: http://localhost:8080/api/v1/health\n\n## 📡 Conversational API Endpoints\n\n### **🧠 Core Conversational Interface**\n- ", "GET /api/v1/health", "GET /api/v1/initialization/status", "GET /api/v1/quote/{symbol}", "GET /api/v1/scan?min_strength={level}", "GET /api/v1/predicto/forecast/{symbol}?days={1-30}", "GET /api/v1/portfolio", "POST /api/v1/risk-assessment", "POST /api/v1/education", "javascript\n// Natural language trading requests\nPOST /api/v1/chat\n{\n  \"message\": \"I want to make $50 today, what are my best options?\",\n  \"session_id\": \"user-123\"\n}\n\n// Educational questions with context\nPOST /api/v1/chat\n{\n  \"message\": \"Explain RSI like I'm a beginner and show me how to use it\",\n  \"session_id\": \"user-123\"\n}\n\n// Goal-oriented analysis\nPOST /api/v1/chat\n{\n  \"message\": \"Analyze AAPL for a swing trade with $500 risk\",\n  \"session_id\": \"user-123\"\n}\n", "javascript\n// User: \"I want to make $100 today\"\nconst response = await fetch('/api/v1/chat', {\n    method: 'POST',\n    headers: { 'Content-Type': 'application/json' },\n    body: JSON.stringify({\n        message: \"I want to make $100 today\",\n        session_id: \"user-session-123\"\n    })\n});\n\n// A.T.L.A.S Response:\n// \"🎯 Goal Set: $100 profit target for today\n//\n// I've stored this goal and will help you work toward it. For a $100 target,\n// I recommend 2-3 smaller trades rather than one big swing.\n//\n// 📊 Analysis for AAPL:\n// **Recommendation:** BUY\n// **Confidence:** 75%\n// **Position Size:** 10 shares ($1,500 position)\n// **Potential Profit:** $25-30 (25% toward your goal)\n//\n// 🎯 Goal Progress: This trade could get you 25% toward your $100 target.\n//\n// 💡 What's next?\n// • Shall I proceed with this trade recommendation?\n// • Want me to find a second setup to complete your goal?\n// • Should I explain the risk/reward calculation?\"\n", "javascript\n// User: \"Analyze AAPL and explain it like I'm a beginner\"\nconst response = await fetch('/api/v1/chat', {\n    method: 'POST',\n    headers: { 'Content-Type': 'application/json' },\n    body: JSON.stringify({\n        message: \"Analyze AAPL and explain it like I'm a beginner\",\n        session_id: \"user-session-123\"\n    })\n});\n\n// A.T.L.A.S Response:\n// \"📊 AAPL Analysis - Beginner-Friendly Explanation\n//\n// **Current Situation**: AAPL is like a car that's been accelerating uphill 🚗⬆️\n//\n// **Technical Analysis**:\n// • **Price**: $150.25 (up 2.1% today)\n// • **RSI**: 65 (think of this like a speedometer - not too fast, not too slow)\n// • **TTM Squeeze**: FIRING LONG (like a rubber band that just snapped upward)\n//\n// **What This Means**:\n// The stock has good momentum without being 'overheated'. It's like finding\n// a car in the perfect gear for climbing a hill.\n//\n// **Trading Opportunity**:\n// • **Entry**: $150.25\n// • **Stop Loss**: $147.50 (your safety net - like wearing a seatbelt)\n// • **Target**: $155.00 (realistic profit target)\n// • **Risk/Reward**: 1:1.7 (risk $2.75 to make $4.75)\n//\n// 📚 **Learning Note**: This setup follows the TTM Squeeze methodology from\n// 'Technical Analysis Explained' - when the squeeze fires, it often leads to\n// sustained moves in that direction.\n//\n// 💡 **What's next?**\n// • Want me to explain any of these concepts in more detail?\n// • Should I show you how I calculated the stop loss?\n// • Ready to place this trade?\"\n", "javascript\n// Get TTM Squeeze signals with educational context\nconst signals = await fetch('/api/v1/scan?min_strength=strong');\nconst data = await signals.json();\n\n// Response includes educational explanations:\n// {\n//   \"signals\": [\n//     {\n//       \"symbol\": \"AAPL\",\n//       \"signal_strength\": \"very_strong\",\n//       \"explanation\": \"TTM Squeeze firing with high volume confirmation - like a coiled spring releasing energy\",\n//       \"educational_note\": \"This pattern has a 70% success rate historically\",\n//       \"risk_warning\": \"Remember to use proper position sizing - never risk more than 2% of your account\"\n//     }\n//   ],\n//   \"count\": 5,\n//   \"educational_summary\": \"Found 5 high-quality setups. Remember: quality over quantity!\"\n// }\n", "javascript\n// Get AI-enhanced risk analysis with educational explanations\nconst assessment = await fetch('/api/v1/risk-assessment', {\n    method: 'POST',\n    headers: { 'Content-Type': 'application/json' },\n    body: JSON.stringify({\n        symbol: \"TSLA\",\n        timeframe: \"1day\",\n        include_risk: true\n    })\n});\n\n// Response includes mentor-style guidance:\n// {\n//   \"symbol\": \"TSLA\",\n//   \"risk_level\": \"moderate\",\n//   \"position_size_recommendation\": \"5% of portfolio maximum\",\n//   \"stop_loss\": \"$245.50\",\n//   \"explanation\": \"TSLA is like a sports car - exciting but requires careful handling\",\n//   \"educational_notes\": [\n//     \"High volatility stocks need smaller position sizes\",\n//     \"Always set your stop loss before entering the trade\",\n//     \"Tesla often moves 3-5% in a day, so plan accordingly\"\n//   ],\n//   \"confidence\": 0.85\n// }\n", "/api/v1/health"], "implemented_endpoints": ["/", "/api/v1/health", "/api/v1/quote/{symbol}", "/api/v1/scan", "/api/v1/predicto/forecast/{symbol}", "/api/v1/portfolio", "/api/v1/initialization/status", "/api/v1/market/news/{symbol}", "/api/v1/market/context/{symbol}", "/api/v1/portfolio/risk-analysis", "/api/v1/portfolio/hedging/{symbol}", "/api/v1/portfolio/optimization", "/api/v1/trading/pending-trades", "/api/v1/chat", "/api/v1/education", "/api/v1/risk-assessment", "/api/v1/portfolio/auto-reinvestment", "/api/v1/trading/prepare-trade", "/api/v1/trading/confirm-trade"], "missing_endpoints": ["/api/v1/scan?min_strength={level}", "/api/v1/predicto/forecast/{symbol}?days={1-30}", "\n\n### **4. Access the System**\n- **Web Interface**: http://localhost:8080\n- **API Documentation**: http://localhost:8080/docs\n- **Health Check**: http://localhost:8080/api/v1/health\n\n## 📡 Conversational API Endpoints\n\n### **🧠 Core Conversational Interface**\n- ", "GET /api/v1/health", "GET /api/v1/initialization/status", "GET /api/v1/quote/{symbol}", "GET /api/v1/scan?min_strength={level}", "GET /api/v1/predicto/forecast/{symbol}?days={1-30}", "GET /api/v1/portfolio", "POST /api/v1/risk-assessment", "POST /api/v1/education", "javascript\n// Natural language trading requests\nPOST /api/v1/chat\n{\n  \"message\": \"I want to make $50 today, what are my best options?\",\n  \"session_id\": \"user-123\"\n}\n\n// Educational questions with context\nPOST /api/v1/chat\n{\n  \"message\": \"Explain RSI like I'm a beginner and show me how to use it\",\n  \"session_id\": \"user-123\"\n}\n\n// Goal-oriented analysis\nPOST /api/v1/chat\n{\n  \"message\": \"Analyze AAPL for a swing trade with $500 risk\",\n  \"session_id\": \"user-123\"\n}\n", "javascript\n// User: \"I want to make $100 today\"\nconst response = await fetch('/api/v1/chat', {\n    method: 'POST',\n    headers: { 'Content-Type': 'application/json' },\n    body: JSON.stringify({\n        message: \"I want to make $100 today\",\n        session_id: \"user-session-123\"\n    })\n});\n\n// A.T.L.A.S Response:\n// \"🎯 Goal Set: $100 profit target for today\n//\n// I've stored this goal and will help you work toward it. For a $100 target,\n// I recommend 2-3 smaller trades rather than one big swing.\n//\n// 📊 Analysis for AAPL:\n// **Recommendation:** BUY\n// **Confidence:** 75%\n// **Position Size:** 10 shares ($1,500 position)\n// **Potential Profit:** $25-30 (25% toward your goal)\n//\n// 🎯 Goal Progress: This trade could get you 25% toward your $100 target.\n//\n// 💡 What's next?\n// • Shall I proceed with this trade recommendation?\n// • Want me to find a second setup to complete your goal?\n// • Should I explain the risk/reward calculation?\"\n", "javascript\n// User: \"Analyze AAPL and explain it like I'm a beginner\"\nconst response = await fetch('/api/v1/chat', {\n    method: 'POST',\n    headers: { 'Content-Type': 'application/json' },\n    body: JSON.stringify({\n        message: \"Analyze AAPL and explain it like I'm a beginner\",\n        session_id: \"user-session-123\"\n    })\n});\n\n// A.T.L.A.S Response:\n// \"📊 AAPL Analysis - Beginner-Friendly Explanation\n//\n// **Current Situation**: AAPL is like a car that's been accelerating uphill 🚗⬆️\n//\n// **Technical Analysis**:\n// • **Price**: $150.25 (up 2.1% today)\n// • **RSI**: 65 (think of this like a speedometer - not too fast, not too slow)\n// • **TTM Squeeze**: FIRING LONG (like a rubber band that just snapped upward)\n//\n// **What This Means**:\n// The stock has good momentum without being 'overheated'. It's like finding\n// a car in the perfect gear for climbing a hill.\n//\n// **Trading Opportunity**:\n// • **Entry**: $150.25\n// • **Stop Loss**: $147.50 (your safety net - like wearing a seatbelt)\n// • **Target**: $155.00 (realistic profit target)\n// • **Risk/Reward**: 1:1.7 (risk $2.75 to make $4.75)\n//\n// 📚 **Learning Note**: This setup follows the TTM Squeeze methodology from\n// 'Technical Analysis Explained' - when the squeeze fires, it often leads to\n// sustained moves in that direction.\n//\n// 💡 **What's next?**\n// • Want me to explain any of these concepts in more detail?\n// • Should I show you how I calculated the stop loss?\n// • Ready to place this trade?\"\n", "javascript\n// Get TTM Squeeze signals with educational context\nconst signals = await fetch('/api/v1/scan?min_strength=strong');\nconst data = await signals.json();\n\n// Response includes educational explanations:\n// {\n//   \"signals\": [\n//     {\n//       \"symbol\": \"AAPL\",\n//       \"signal_strength\": \"very_strong\",\n//       \"explanation\": \"TTM Squeeze firing with high volume confirmation - like a coiled spring releasing energy\",\n//       \"educational_note\": \"This pattern has a 70% success rate historically\",\n//       \"risk_warning\": \"Remember to use proper position sizing - never risk more than 2% of your account\"\n//     }\n//   ],\n//   \"count\": 5,\n//   \"educational_summary\": \"Found 5 high-quality setups. Remember: quality over quantity!\"\n// }\n", "javascript\n// Get AI-enhanced risk analysis with educational explanations\nconst assessment = await fetch('/api/v1/risk-assessment', {\n    method: 'POST',\n    headers: { 'Content-Type': 'application/json' },\n    body: JSON.stringify({\n        symbol: \"TSLA\",\n        timeframe: \"1day\",\n        include_risk: true\n    })\n});\n\n// Response includes mentor-style guidance:\n// {\n//   \"symbol\": \"TSLA\",\n//   \"risk_level\": \"moderate\",\n//   \"position_size_recommendation\": \"5% of portfolio maximum\",\n//   \"stop_loss\": \"$245.50\",\n//   \"explanation\": \"TSLA is like a sports car - exciting but requires careful handling\",\n//   \"educational_notes\": [\n//     \"High volatility stocks need smaller position sizes\",\n//     \"Always set your stop loss before entering the trade\",\n//     \"Tesla often moves 3-5% in a day, so plan accordingly\"\n//   ],\n//   \"confidence\": 0.85\n// }\n"], "undocumented_endpoints": ["/", "/api/v1/scan", "/api/v1/predicto/forecast/{symbol}", "/api/v1/market/news/{symbol}", "/api/v1/market/context/{symbol}", "/api/v1/portfolio/risk-analysis", "/api/v1/portfolio/hedging/{symbol}", "/api/v1/portfolio/optimization", "/api/v1/trading/pending-trades", "/api/v1/portfolio/auto-reinvestment", "/api/v1/trading/prepare-trade", "/api/v1/trading/confirm-trade"], "total_endpoints": 19}, "execution_time": 0.000811, "timestamp": "2025-06-27T17:13:35.064691"}], "integration": [{"test_name": "integration_validation", "category": "integration", "status": "failed", "message": "Integration failures: 8", "details": {"successful_integrations": [], "failed_integrations": ["orchestrator_not_available", "orchestrator_not_available", "ai_engine_not_available", "trading_engine_not_available", "market_engine_not_available", "education_engine_not_available", "risk_engine_not_available", "server_not_available"], "components_available": 0, "total_components": 7}, "execution_time": 0.000112, "timestamp": "2025-06-27T17:13:35.064925"}], "critical_path": [{"test_name": "critical_paths_validation", "category": "critical_path", "status": "failed", "message": "Critical path failures: 4", "details": {"successful_paths": [], "failed_paths": ["ai_engine_not_available", "market_engine_not_available", "trading_engine_not_available", "education_engine_not_available"], "total_paths_tested": 4}, "execution_time": 0.000106, "timestamp": "2025-06-27T17:13:35.065158"}], "safety_risk": [{"test_name": "safety_risk_validation", "category": "safety_risk", "status": "failed", "message": "Safety system failures: 2", "details": {"safety_systems_operational": [], "safety_system_failures": ["trading_engine_not_available", "risk_engine_not_available"], "safety_coverage": 0.0}, "execution_time": 7.2e-05, "timestamp": "2025-06-27T17:13:35.065309"}], "documentation": [{"test_name": "documentation_validation", "category": "documentation", "status": "passed", "message": "Documentation complete and accurate", "details": {"documentation_complete": ["readme_has_installation", "readme_has_quick start", "readme_has_features", "readme_has_api", "readme_has_configuration", "api_documentation_present", "installation_instructions_present", "requirements_file_present", "config_file_present"], "documentation_issues": [], "completeness_score": 100.0}, "execution_time": 0.000891, "timestamp": "2025-06-27T17:13:35.066269"}], "dependencies": [{"test_name": "dependencies_validation", "category": "dependencies", "status": "error", "message": "Validation error: Missing required API keys for production mode: ALPACA_API_KEY, ALPACA_SECRET_KEY. Set VALIDATION_MODE=true to run without API keys for testing.", "details": {"error": "Missing required API keys for production mode: ALPACA_API_KEY, ALPACA_SECRET_KEY. Set VALIDATION_MODE=true to run without API keys for testing."}, "execution_time": 0.631576, "timestamp": "2025-06-27T17:13:35.697969"}]}}