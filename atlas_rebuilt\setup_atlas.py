#!/usr/bin/env python3
"""
A.T.L.A.S Setup Script
Automated setup and validation for the A.T.L.A.S AI Trading System
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def print_banner():
    """Print A.T.L.A.S setup banner"""
    print("=" * 80)
    print("🚀 A.T.L.A.S AI Trading System - Setup & Validation")
    print("   Advanced Trading & Learning Analysis System")
    print("=" * 80)
    print()

def check_python_version():
    """Check if Python version is compatible"""
    print("🐍 Checking Python version...")
    
    if sys.version_info < (3, 8):
        print("❌ Error: Python 3.8 or higher is required")
        print(f"   Current version: {sys.version}")
        return False
    
    print(f"✅ Python {sys.version.split()[0]} - Compatible")
    return True

def install_dependencies():
    """Install required dependencies"""
    print("\n📦 Installing dependencies...")
    
    try:
        # Check if requirements.txt exists
        if not os.path.exists("requirements.txt"):
            print("⚠️  requirements.txt not found - creating basic requirements")
            create_basic_requirements()
        
        # Install requirements
        print("   Installing packages from requirements.txt...")
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Dependencies installed successfully")
            return True
        else:
            print(f"❌ Error installing dependencies: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ Error during dependency installation: {e}")
        return False

def create_basic_requirements():
    """Create basic requirements.txt if it doesn't exist"""
    basic_requirements = """
# Core dependencies
fastapi>=0.104.0
uvicorn>=0.24.0
aiohttp>=3.9.0
pydantic>=2.5.0
python-multipart>=0.0.6

# AI and ML
openai>=1.3.0
numpy>=1.24.0
pandas>=2.0.0

# Optional dependencies (install manually if needed)
# transformers>=4.35.0  # For ML sentiment analysis
# chromadb>=0.4.0       # For RAG system
# google-api-python-client>=2.100.0  # For Google search
# duckduckgo-search>=3.9.0  # For DuckDuckGo search
""".strip()
    
    with open("requirements.txt", "w") as f:
        f.write(basic_requirements)

def setup_configuration():
    """Set up configuration files"""
    print("\n⚙️  Setting up configuration...")
    
    # Copy .env template if .env doesn't exist
    if not os.path.exists(".env"):
        if os.path.exists(".env.template"):
            print("   Creating .env from template...")
            shutil.copy(".env.template", ".env")
            print("✅ .env file created from template")
            print("   📝 Please edit .env file with your API keys")
        else:
            print("⚠️  .env.template not found - you'll need to configure manually")
    else:
        print("✅ .env file already exists")
    
    return True

def run_validation():
    """Run system validation"""
    print("\n🔍 Running system validation...")
    
    try:
        # Set validation mode to avoid API key requirements
        env = os.environ.copy()
        env["VALIDATION_MODE"] = "true"
        
        result = subprocess.run([
            sys.executable, "run_validation.py"
        ], env=env, capture_output=True, text=True)
        
        print("📊 Validation Results:")
        print(result.stdout)
        
        if result.returncode == 0:
            print("✅ Validation passed!")
            return True
        elif result.returncode == 2:
            print("⚠️  Validation passed with warnings")
            return True
        else:
            print("❌ Validation failed - see output above")
            return False
            
    except Exception as e:
        print(f"❌ Error running validation: {e}")
        return False

def print_next_steps():
    """Print next steps for the user"""
    print("\n🎯 Next Steps:")
    print("1. Edit the .env file with your API keys:")
    print("   - ALPACA_API_KEY and ALPACA_SECRET_KEY (for trading)")
    print("   - OPENAI_API_KEY (for AI features)")
    print("   - FMP_API_KEY (for market data)")
    print()
    print("2. Run the system:")
    print("   python start_atlas.py")
    print()
    print("3. Access the web interface:")
    print("   http://localhost:8000")
    print()
    print("4. Run validation again after configuration:")
    print("   python run_validation.py")
    print()
    print("📚 Documentation:")
    print("   - README.md - System overview and features")
    print("   - VALIDATION_SUMMARY.md - Validation details")
    print("   - .env.template - Configuration reference")

def main():
    """Main setup function"""
    print_banner()
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Install dependencies
    if not install_dependencies():
        print("\n❌ Setup failed during dependency installation")
        sys.exit(1)
    
    # Setup configuration
    if not setup_configuration():
        print("\n❌ Setup failed during configuration")
        sys.exit(1)
    
    # Run validation
    validation_passed = run_validation()
    
    # Print results
    print("\n" + "=" * 80)
    if validation_passed:
        print("🎉 A.T.L.A.S Setup Complete!")
    else:
        print("⚠️  A.T.L.A.S Setup Complete with Issues")
    print("=" * 80)
    
    print_next_steps()
    
    # Exit with appropriate code
    sys.exit(0 if validation_passed else 1)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n⏹️  Setup interrupted by user")
        sys.exit(130)
    except Exception as e:
        print(f"\n💥 Setup failed with error: {e}")
        sys.exit(1)
