#!/usr/bin/env python3
"""
A.T.L.A.S Production Runner - REAL API KEYS, REAL TRADING
"""

import os
import sys
import subprocess
from pathlib import Path

def load_production_env():
    """Load production environment variables"""
    env_file = Path(".env.production")
    
    if not env_file.exists():
        print("❌ .env.production file not found!")
        return False
    
    print("🔑 Loading production API keys...")
    
    with open(env_file, 'r') as f:
        for line in f:
            line = line.strip()
            if line and not line.startswith('#') and '=' in line:
                key, value = line.split('=', 1)
                os.environ[key] = value
                
                # Mask sensitive keys for display
                if 'KEY' in key or 'SECRET' in key:
                    display_value = value[:8] + '*' * (len(value) - 12) + value[-4:] if len(value) > 12 else '*' * len(value)
                    print(f"  ✅ {key}: {display_value}")
                else:
                    print(f"  ✅ {key}: {value}")
    
    return True

def check_api_keys():
    """Verify all required API keys are present"""
    required_keys = [
        "ALPACA_API_KEY",
        "FMP_API_KEY", 
        "OPENAI_API_KEY",
        "PREDICTO_API_KEY"
    ]
    
    missing_keys = []
    for key in required_keys:
        if not os.environ.get(key):
            missing_keys.append(key)
    
    if missing_keys:
        print(f"❌ Missing API keys: {', '.join(missing_keys)}")
        return False
    
    print("✅ All required API keys present")
    return True

def start_production_server():
    """Start A.T.L.A.S in production mode"""
    print("\n🚀 Starting A.T.L.A.S PRODUCTION SERVER")
    print("=" * 50)
    print("🔥 LIVE TRADING MODE ACTIVE")
    print("📊 Real-time market data enabled")
    print("🧠 Full AI analysis capabilities")
    print("📈 Predicto AI predictions active")
    print("🌐 Server: http://localhost:8080")
    print("📚 API Docs: http://localhost:8080/docs")
    print("=" * 50)
    print("⚠️  WARNING: This system can execute REAL trades with REAL money!")
    print("⚠️  Make sure you understand the risks before proceeding!")
    print("=" * 50)
    
    try:
        # Import and run the server
        from atlas_server_fixed import app
        import uvicorn
        
        uvicorn.run(
            app,
            host="0.0.0.0",
            port=int(os.environ.get("PORT", 8080)),
            log_level=os.environ.get("LOG_LEVEL", "info").lower(),
            reload=False
        )
        
    except KeyboardInterrupt:
        print("\n⏹️ A.T.L.A.S server stopped by user")
    except Exception as e:
        print(f"❌ Server startup failed: {e}")
        import traceback
        traceback.print_exc()

def main():
    """Main production startup function"""
    print("🔥 A.T.L.A.S AI Trading System - PRODUCTION MODE")
    print("=" * 60)
    print("🎯 Advanced Trading & Learning Analysis System v4.0")
    print("💼 LIVE TRADING CAPABILITIES ENABLED")
    print("=" * 60)
    
    # Load environment
    if not load_production_env():
        print("❌ Failed to load production environment")
        return False
    
    # Check API keys
    if not check_api_keys():
        print("❌ API key validation failed")
        return False
    
    # Show current configuration
    print(f"\n📊 Configuration:")
    print(f"  Trading Mode: {'LIVE' if 'paper' not in os.environ.get('ALPACA_BASE_URL', '') else 'PAPER'}")
    print(f"  Validation Mode: {os.environ.get('VALIDATION_MODE', 'false')}")
    print(f"  Port: {os.environ.get('PORT', '8080')}")
    print(f"  Log Level: {os.environ.get('LOG_LEVEL', 'INFO')}")
    
    # Final confirmation
    print(f"\n⚠️  FINAL WARNING:")
    print(f"  - This will enable REAL trading capabilities")
    print(f"  - Real money can be gained or lost")
    print(f"  - All trades require your confirmation")
    print(f"  - Safety limits are in place but USE CAUTION")
    
    response = input(f"\n🚀 Start A.T.L.A.S PRODUCTION server? (yes/no): ").lower().strip()
    
    if response in ['yes', 'y']:
        start_production_server()
        return True
    else:
        print("❌ Production startup cancelled")
        print("💡 For testing mode: python atlas_server_minimal.py")
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⏹️ Startup interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"💥 Startup failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
